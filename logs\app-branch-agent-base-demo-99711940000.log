********-03:30:19.874|INFO|null||***********:null|||21788|background-preinit|org.hibernate.validator.internal.util.Version.?.?|MSG=HV000001: Hibernate Validator 6.2.5.Final
********-03:30:19.894|INFO|null||***********:null|||21788|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=Starting BranchAgentApplication using Java 1.8.0_391 on DESKTOP-KE6OBS4 with PID 21788 (C:\workspace\bank\branch-agent-xm0628\target\classes started by WLF in C:\workspace\bank\branch-agent-xm0628)
********-03:30:19.908|INFO|null||***********:null|||21788|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=The following 1 profile is active: "dev"
********-03:30:20.323|INFO|null||***********:null|||21788|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-03:30:20.385|INFO|null||***********:null|||21788|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-03:30:20.391|INFO|null||***********:null|||21788|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-03:30:24.244|INFO|null||***********:null|||21788|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Multiple Spring Data modules found, entering strict repository configuration mode
********-03:30:24.247|INFO|null||***********:null|||21788|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Bootstrapping Spring Data Redis repositories in DEFAULT mode.
********-03:30:24.308|INFO|null||***********:null|||21788|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
********-03:30:24.599|WARN|null||***********:null|||21788|main|org.mybatis.spring.mapper.ClassPathMapperScanner.?.?|MSG=No MyBatis mapper was found in '[com.psbc.cpufp]' package. Please check your configuration.
********-03:30:24.826|INFO|null||***********:null|||21788|main|org.springframework.cloud.context.scope.GenericScope.?.?|MSG=BeanFactory id=b6824e92-c25e-3eff-8015-68899aab9a9a
********-03:30:25.665|INFO|null||***********:null|||21788|main|org.springframework.boot.web.embedded.tomcat.TomcatWebServer.?.?|MSG=Tomcat initialized with port(s): 6666 (http)
********-03:30:25.681|INFO|null||***********:null|||21788|main|org.apache.coyote.http11.Http11NioProtocol.?.?|MSG=Initializing ProtocolHandler ["http-nio-6666"]
********-03:30:25.682|INFO|null||***********:null|||21788|main|org.apache.catalina.core.StandardService.?.?|MSG=Starting service [Tomcat]
********-03:30:25.682|INFO|null||***********:null|||21788|main|org.apache.catalina.core.StandardEngine.?.?|MSG=Starting Servlet engine: [Apache Tomcat/9.0.73]
********-03:30:25.895|INFO|null||***********:null|||21788|main|org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].?.?|MSG=Initializing Spring embedded WebApplicationContext
********-03:30:25.896|INFO|null||***********:null|||21788|main|org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.?.?|MSG=Root WebApplicationContext: initialization completed in 5904 ms
********-03:30:27.682|INFO|null||***********:6666|||21788|main|org.soma.job.client.SomaRpcProviderConfig.?.?|MSG=...SOMA>>>>>>>>>>>start
********-03:30:27.684|INFO|null||***********:6666|||21788|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init start after 0s
********-03:30:27.684|INFO|null||***********:6666|||21788|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>SomaProperties(initDelayTime=0, loggerAdapter=, remoteMaxThreadNum=3, remotePort=9944, execVersion=default, testFlag=0, coreAccessToken=, registryUrl=soma-kernel.cpufp-plat:20081, execAccessToken=b79558f7390c45d8bec48aa03fabaaa7, isAutoRegistry=1, execGroupId=EIDbfc5af50-005c-11ee-883c-fa163e7876be, env=default, isOpenDefaultLog=0)
********-03:30:27.846|INFO|null||***********:6666|||21788|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init RpcSpringProviderFactory start-----------------------------
********-03:30:27.850|INFO|null||***********:6666|||21788|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init RpcSpringProviderFactory end-----------------------------
********-03:30:27.851|INFO|null||***********:6666|||21788|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>scan client executor class start
********-03:30:27.857|INFO|null||***********:6666|||21788|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>scan client executor class end
********-03:30:27.859|INFO|null||***********:6666|||21788|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>soma-job use port:9944
********-03:30:29.245|INFO|null||***********:6666|||21788|Thread-28|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=server started, listening on 9944
********-03:30:29.246|INFO|null||***********:6666|||21788|Thread-28|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>soma Auto registry is not use.  config param [isAutoRegistry]=1
********-03:30:31.711|INFO|null||***********:6666|||21788|main|org.springframework.cloud.commons.util.InetUtils.?.?|MSG=Cannot determine local hostname
********-03:30:31.776|INFO|null||***********:6666|||21788|main|org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver.?.?|MSG=Exposing 1 endpoint(s) beneath base path '/actuator'
********-03:30:31.907|INFO|null||***********:6666|||21788|main|org.apache.coyote.http11.Http11NioProtocol.?.?|MSG=Starting ProtocolHandler ["http-nio-6666"]
********-03:30:31.927|INFO|null||***********:6666|||21788|main|org.springframework.boot.web.embedded.tomcat.TomcatWebServer.?.?|MSG=Tomcat started on port(s): 6666 (http) with context path ''
********-03:30:33.475|INFO|null||***********:6666|||21788|main|org.springframework.cloud.commons.util.InetUtils.?.?|MSG=Cannot determine local hostname
********-03:30:33.501|INFO|null||***********:6666|||21788|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=Started BranchAgentApplication in 16.663 seconds (JVM running for 20.832)
********-03:30:34.013|INFO|null||***********:6666|||21788|RMI TCP Connection(2)-**************|org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].?.?|MSG=Initializing Spring DispatcherServlet 'dispatcherServlet'
********-03:30:34.014|INFO|null||***********:6666|||21788|RMI TCP Connection(2)-**************|org.springframework.web.servlet.DispatcherServlet.?.?|MSG=Initializing Servlet 'dispatcherServlet'
********-03:30:34.017|INFO|null||***********:6666|||21788|RMI TCP Connection(2)-**************|org.springframework.web.servlet.DispatcherServlet.?.?|MSG=Completed initialization in 2 ms
********-03:30:34.017|INFO|null||***********:6666|||21788|RMI TCP Connection(1)-**************|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Starting...
********-03:30:34.406|INFO|null||***********:6666|||21788|RMI TCP Connection(1)-**************|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Start completed.
********-04:02:15.946|INFO|null||***********:6666|||21788|Thread-31|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=*** shutting down soma server since JVM is shutting down
********-04:02:15.998|INFO|null||***********:6666|||21788|Thread-31|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=*** server shut down
********-04:02:16.876|INFO|null||***********:6666|||21788|SpringApplicationShutdownHook|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Shutdown initiated...
********-04:02:16.892|INFO|null||***********:6666|||21788|SpringApplicationShutdownHook|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Shutdown completed.
********-04:02:34.757|INFO|null||***********:null|||10348|background-preinit|org.hibernate.validator.internal.util.Version.?.?|MSG=HV000001: Hibernate Validator 6.2.5.Final
********-04:02:34.779|INFO|null||***********:null|||10348|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=Starting BranchAgentApplication using Java 1.8.0_391 on DESKTOP-KE6OBS4 with PID 10348 (C:\workspace\bank\branch-agent-xm0628\target\classes started by WLF in C:\workspace\bank\branch-agent-xm0628)
********-04:02:34.791|INFO|null||***********:null|||10348|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=The following 1 profile is active: "dev"
********-04:02:35.240|INFO|null||***********:null|||10348|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-04:02:35.333|INFO|null||***********:null|||10348|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-04:02:35.340|INFO|null||***********:null|||10348|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-04:02:39.585|INFO|null||***********:null|||10348|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Multiple Spring Data modules found, entering strict repository configuration mode
********-04:02:39.589|INFO|null||***********:null|||10348|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Bootstrapping Spring Data Redis repositories in DEFAULT mode.
********-04:02:39.650|INFO|null||***********:null|||10348|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
********-04:02:39.948|WARN|null||***********:null|||10348|main|org.mybatis.spring.mapper.ClassPathMapperScanner.?.?|MSG=No MyBatis mapper was found in '[com.psbc.cpufp]' package. Please check your configuration.
********-04:02:40.213|INFO|null||***********:null|||10348|main|org.springframework.cloud.context.scope.GenericScope.?.?|MSG=BeanFactory id=b6824e92-c25e-3eff-8015-68899aab9a9a
********-04:02:41.184|INFO|null||***********:null|||10348|main|org.springframework.boot.web.embedded.tomcat.TomcatWebServer.?.?|MSG=Tomcat initialized with port(s): 6666 (http)
********-04:02:41.204|INFO|null||***********:null|||10348|main|org.apache.coyote.http11.Http11NioProtocol.?.?|MSG=Initializing ProtocolHandler ["http-nio-6666"]
********-04:02:41.205|INFO|null||***********:null|||10348|main|org.apache.catalina.core.StandardService.?.?|MSG=Starting service [Tomcat]
********-04:02:41.205|INFO|null||***********:null|||10348|main|org.apache.catalina.core.StandardEngine.?.?|MSG=Starting Servlet engine: [Apache Tomcat/9.0.73]
********-04:02:41.453|INFO|null||***********:null|||10348|main|org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].?.?|MSG=Initializing Spring embedded WebApplicationContext
********-04:02:41.453|INFO|null||***********:null|||10348|main|org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.?.?|MSG=Root WebApplicationContext: initialization completed in 6580 ms
********-04:02:43.367|INFO|null||***********:6666|||10348|main|org.soma.job.client.SomaRpcProviderConfig.?.?|MSG=...SOMA>>>>>>>>>>>start
********-04:02:43.368|INFO|null||***********:6666|||10348|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init start after 0s
********-04:02:43.368|INFO|null||***********:6666|||10348|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>SomaProperties(initDelayTime=0, loggerAdapter=, remoteMaxThreadNum=3, remotePort=9944, execVersion=default, testFlag=0, coreAccessToken=, registryUrl=soma-kernel.cpufp-plat:20081, execAccessToken=b79558f7390c45d8bec48aa03fabaaa7, isAutoRegistry=1, execGroupId=EIDbfc5af50-005c-11ee-883c-fa163e7876be, env=default, isOpenDefaultLog=0)
********-04:02:43.466|INFO|null||***********:6666|||10348|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init RpcSpringProviderFactory start-----------------------------
********-04:02:43.471|INFO|null||***********:6666|||10348|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init RpcSpringProviderFactory end-----------------------------
********-04:02:43.471|INFO|null||***********:6666|||10348|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>scan client executor class start
********-04:02:43.476|INFO|null||***********:6666|||10348|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>scan client executor class end
********-04:02:43.479|INFO|null||***********:6666|||10348|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>soma-job use port:9944
********-04:02:44.814|INFO|null||***********:6666|||10348|Thread-30|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=server started, listening on 9944
********-04:02:44.816|INFO|null||***********:6666|||10348|Thread-30|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>soma Auto registry is not use.  config param [isAutoRegistry]=1
********-04:02:47.411|INFO|null||***********:6666|||10348|main|org.springframework.cloud.commons.util.InetUtils.?.?|MSG=Cannot determine local hostname
********-04:02:47.485|INFO|null||***********:6666|||10348|main|org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver.?.?|MSG=Exposing 1 endpoint(s) beneath base path '/actuator'
********-04:02:47.625|INFO|null||***********:6666|||10348|main|org.apache.coyote.http11.Http11NioProtocol.?.?|MSG=Starting ProtocolHandler ["http-nio-6666"]
********-04:02:47.656|INFO|null||***********:6666|||10348|main|org.springframework.boot.web.embedded.tomcat.TomcatWebServer.?.?|MSG=Tomcat started on port(s): 6666 (http) with context path ''
********-04:02:49.261|INFO|null||***********:6666|||10348|main|org.springframework.cloud.commons.util.InetUtils.?.?|MSG=Cannot determine local hostname
********-04:02:49.306|INFO|null||***********:6666|||10348|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=Started BranchAgentApplication in 17.658 seconds (JVM running for 20.179)
********-04:02:50.206|INFO|null||***********:6666|||10348|RMI TCP Connection(1)-**************|org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].?.?|MSG=Initializing Spring DispatcherServlet 'dispatcherServlet'
********-04:02:50.206|INFO|null||***********:6666|||10348|RMI TCP Connection(1)-**************|org.springframework.web.servlet.DispatcherServlet.?.?|MSG=Initializing Servlet 'dispatcherServlet'
********-04:02:50.210|INFO|null||***********:6666|||10348|RMI TCP Connection(1)-**************|org.springframework.web.servlet.DispatcherServlet.?.?|MSG=Completed initialization in 4 ms
********-04:02:50.212|INFO|null||***********:6666|||10348|RMI TCP Connection(2)-**************|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Starting...
********-04:02:50.619|INFO|null||***********:6666|||10348|RMI TCP Connection(2)-**************|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Start completed.
********-04:20:33.269|WARN|null||***********:6666|||10348|http-nio-6666-exec-2|org.apache.cxf.transport.servlet.ServletController.?.?|MSG=Can't find the request for http://localhost:6666/services/IWebServiceService's Observer 
********-04:20:33.292|WARN|null||***********:6666|||10348|http-nio-6666-exec-1|org.apache.cxf.transport.servlet.ServletController.?.?|MSG=Can't find the request for http://localhost:6666/services/IWebServiceService's Observer 
********-04:20:33.302|WARN|null||***********:6666|||10348|http-nio-6666-exec-3|org.apache.cxf.transport.servlet.ServletController.?.?|MSG=Can't find the request for http://localhost:6666/services/IWebServiceService's Observer 
********-04:20:33.307|WARN|null||***********:6666|||10348|http-nio-6666-exec-4|org.apache.cxf.transport.servlet.ServletController.?.?|MSG=Can't find the request for http://localhost:6666/services/IWebServiceService's Observer 
********-04:20:33.317|WARN|null||***********:6666|||10348|http-nio-6666-exec-5|org.apache.cxf.transport.servlet.ServletController.?.?|MSG=Can't find the request for http://localhost:6666/services/IWebServiceService's Observer 
********-04:20:33.329|WARN|null||***********:6666|||10348|http-nio-6666-exec-6|org.apache.cxf.transport.servlet.ServletController.?.?|MSG=Can't find the request for http://localhost:6666/services/IWebServiceService's Observer 
********-04:20:33.333|WARN|null||***********:6666|||10348|http-nio-6666-exec-7|org.apache.cxf.transport.servlet.ServletController.?.?|MSG=Can't find the request for http://localhost:6666/services/IWebServiceService's Observer 
********-04:20:33.340|WARN|null||***********:6666|||10348|http-nio-6666-exec-8|org.apache.cxf.transport.servlet.ServletController.?.?|MSG=Can't find the request for http://localhost:6666/services/IWebServiceService's Observer 
********-04:20:33.343|WARN|null||***********:6666|||10348|http-nio-6666-exec-9|org.apache.cxf.transport.servlet.ServletController.?.?|MSG=Can't find the request for http://localhost:6666/services/IWebServiceService's Observer 
********-04:20:33.349|WARN|null||***********:6666|||10348|http-nio-6666-exec-10|org.apache.cxf.transport.servlet.ServletController.?.?|MSG=Can't find the request for http://localhost:6666/services/IWebServiceService's Observer 
********-13:07:33.271|INFO|null||***********:6666|||10348|Thread-33|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=*** shutting down soma server since JVM is shutting down
********-13:07:33.350|INFO|null||***********:6666|||10348|Thread-33|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=*** server shut down
********-13:07:35.519|INFO|null||***********:6666|||10348|SpringApplicationShutdownHook|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Shutdown initiated...
********-13:07:35.537|INFO|null||***********:6666|||10348|SpringApplicationShutdownHook|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Shutdown completed.
********-13:08:11.858|INFO|null||***********:null|||24660|background-preinit|org.hibernate.validator.internal.util.Version.?.?|MSG=HV000001: Hibernate Validator 6.2.5.Final
********-13:08:11.897|INFO|null||***********:null|||24660|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=Starting BranchAgentApplication using Java 1.8.0_391 on DESKTOP-KE6OBS4 with PID 24660 (C:\workspace\bank\branch-agent-xm0628\target\classes started by WLF in C:\workspace\bank\branch-agent-xm0628)
********-13:08:11.899|INFO|null||***********:null|||24660|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=The following 1 profile is active: "dev"
********-13:08:12.648|INFO|null||***********:null|||24660|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-13:08:12.744|INFO|null||***********:null|||24660|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-13:08:12.750|INFO|null||***********:null|||24660|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-13:08:17.171|INFO|null||***********:null|||24660|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Multiple Spring Data modules found, entering strict repository configuration mode
********-13:08:17.178|INFO|null||***********:null|||24660|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Bootstrapping Spring Data Redis repositories in DEFAULT mode.
********-13:08:17.265|INFO|null||***********:null|||24660|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Finished Spring Data repository scanning in 43 ms. Found 0 Redis repository interfaces.
********-13:08:17.621|WARN|null||***********:null|||24660|main|org.mybatis.spring.mapper.ClassPathMapperScanner.?.?|MSG=No MyBatis mapper was found in '[com.psbc.cpufp]' package. Please check your configuration.
********-13:08:17.775|INFO|null||***********:null|||24660|main|org.springframework.cloud.context.scope.GenericScope.?.?|MSG=BeanFactory id=9318c593-9ef8-338d-9e0c-72e1db20d6ef
********-13:08:18.643|INFO|null||***********:null|||24660|main|org.springframework.boot.web.embedded.tomcat.TomcatWebServer.?.?|MSG=Tomcat initialized with port(s): 6666 (http)
********-13:08:18.660|INFO|null||***********:null|||24660|main|org.apache.coyote.http11.Http11NioProtocol.?.?|MSG=Initializing ProtocolHandler ["http-nio-6666"]
********-13:08:18.661|INFO|null||***********:null|||24660|main|org.apache.catalina.core.StandardService.?.?|MSG=Starting service [Tomcat]
********-13:08:18.661|INFO|null||***********:null|||24660|main|org.apache.catalina.core.StandardEngine.?.?|MSG=Starting Servlet engine: [Apache Tomcat/9.0.73]
********-13:08:19.020|INFO|null||***********:null|||24660|main|org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].?.?|MSG=Initializing Spring embedded WebApplicationContext
********-13:08:19.020|INFO|null||***********:null|||24660|main|org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.?.?|MSG=Root WebApplicationContext: initialization completed in 7011 ms
********-13:08:19.680|INFO|null||***********:null|||24660|main|org.springframework.boot.web.servlet.RegistrationBean.?.?|MSG=Servlet CXFServlet was not registered (possibly already registered?)
********-13:08:21.454|INFO|null||***********:6666|||24660|main|org.soma.job.client.SomaRpcProviderConfig.?.?|MSG=...SOMA>>>>>>>>>>>start
********-13:08:21.455|INFO|null||***********:6666|||24660|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init start after 0s
********-13:08:21.455|INFO|null||***********:6666|||24660|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>SomaProperties(initDelayTime=0, loggerAdapter=, remoteMaxThreadNum=3, remotePort=9944, execVersion=default, testFlag=0, coreAccessToken=, registryUrl=soma-kernel.cpufp-plat:20081, execAccessToken=b79558f7390c45d8bec48aa03fabaaa7, isAutoRegistry=1, execGroupId=EIDbfc5af50-005c-11ee-883c-fa163e7876be, env=default, isOpenDefaultLog=0)
********-13:08:21.782|INFO|null||***********:6666|||24660|main|org.apache.cxf.wsdl.service.factory.ReflectionServiceFactoryBean.?.?|MSG=Creating Service {http://impl.service.cpufp.psbc.com/}IWebServiceService from class com.psbc.cpufp.service.IWebService
********-13:08:22.868|ERROR|null||***********:6666|||24660|main|org.apache.cxf.transport.http.HTTPTransportFactory.?.?|MSG=Cannot find any registered HttpDestinationFactory from the Bus.
********-13:08:22.875|WARN|null||***********:6666|||24660|main|org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext.?.?|MSG=Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'endpoint' defined in class path resource [com/psbc/cpufp/config/WebServiceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [javax.xml.ws.Endpoint]: Factory method 'endpoint' threw exception; nested exception is javax.xml.ws.WebServiceException: org.apache.cxf.service.factory.ServiceConstructionException
********-13:08:22.929|INFO|null||***********:6666|||24660|main|org.apache.catalina.core.StandardService.?.?|MSG=Stopping service [Tomcat]
********-13:08:22.960|INFO|null||***********:6666|||24660|main|org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.?.?|MSG=

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
********-13:08:23.023|ERROR|null||***********:6666|||24660|main|org.springframework.boot.SpringApplication.?.?|MSG=Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'endpoint' defined in class path resource [com/psbc/cpufp/config/WebServiceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [javax.xml.ws.Endpoint]: Factory method 'endpoint' threw exception; nested exception is javax.xml.ws.WebServiceException: org.apache.cxf.service.factory.ServiceConstructionException
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:486)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:920)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.psbc.cpufp.BranchAgentApplication.main(BranchAgentApplication.java:16)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [javax.xml.ws.Endpoint]: Factory method 'endpoint' threw exception; nested exception is javax.xml.ws.WebServiceException: org.apache.cxf.service.factory.ServiceConstructionException
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 19 common frames omitted
Caused by: javax.xml.ws.WebServiceException: org.apache.cxf.service.factory.ServiceConstructionException
	at org.apache.cxf.jaxws.EndpointImpl.doPublish(EndpointImpl.java:373)
	at org.apache.cxf.jaxws.EndpointImpl.publish(EndpointImpl.java:255)
	at com.psbc.cpufp.config.WebServiceConfig.endpoint(WebServiceConfig.java:44)
	at com.psbc.cpufp.config.WebServiceConfig$$EnhancerBySpringCGLIB$$c38beb2.CGLIB$endpoint$0(<generated>)
	at com.psbc.cpufp.config.WebServiceConfig$$EnhancerBySpringCGLIB$$c38beb2$$FastClassBySpringCGLIB$$92d4218.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.psbc.cpufp.config.WebServiceConfig$$EnhancerBySpringCGLIB$$c38beb2.endpoint(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 20 common frames omitted
Caused by: org.apache.cxf.service.factory.ServiceConstructionException: null
	at org.apache.cxf.frontend.ServerFactoryBean.create(ServerFactoryBean.java:193)
	at org.apache.cxf.jaxws.JaxWsServerFactoryBean.create(JaxWsServerFactoryBean.java:211)
	at org.apache.cxf.jaxws.EndpointImpl.getServer(EndpointImpl.java:458)
	at org.apache.cxf.jaxws.EndpointImpl.doPublish(EndpointImpl.java:336)
	... 32 common frames omitted
Caused by: java.io.IOException: Cannot find any registered HttpDestinationFactory from the Bus.
	at org.apache.cxf.transport.http.HTTPTransportFactory.getDestination(HTTPTransportFactory.java:276)
	at org.apache.cxf.binding.soap.SoapTransportFactory.getDestination(SoapTransportFactory.java:135)
	at org.apache.cxf.endpoint.ServerImpl.initDestination(ServerImpl.java:86)
	at org.apache.cxf.endpoint.ServerImpl.<init>(ServerImpl.java:65)
	at org.apache.cxf.frontend.ServerFactoryBean.create(ServerFactoryBean.java:182)
	... 35 common frames omitted
********-16:10:01.890|INFO|null||***********:null|||7800|background-preinit|org.hibernate.validator.internal.util.Version.?.?|MSG=HV000001: Hibernate Validator 6.2.5.Final
********-16:10:01.929|INFO|null||***********:null|||7800|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=Starting BranchAgentApplication using Java 1.8.0_391 on DESKTOP-KE6OBS4 with PID 7800 (C:\workspace\bank\branch-agent-xm0628\target\classes started by WLF in C:\workspace\bank\branch-agent-xm0628)
********-16:10:01.930|INFO|null||***********:null|||7800|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=The following 1 profile is active: "dev"
********-16:10:02.625|INFO|null||***********:null|||7800|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-16:10:02.755|INFO|null||***********:null|||7800|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-16:10:02.764|INFO|null||***********:null|||7800|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-16:10:10.572|INFO|null||***********:null|||7800|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Multiple Spring Data modules found, entering strict repository configuration mode
********-16:10:10.582|INFO|null||***********:null|||7800|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Bootstrapping Spring Data Redis repositories in DEFAULT mode.
********-16:10:10.694|INFO|null||***********:null|||7800|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Finished Spring Data repository scanning in 50 ms. Found 0 Redis repository interfaces.
********-16:10:11.169|WARN|null||***********:null|||7800|main|org.mybatis.spring.mapper.ClassPathMapperScanner.?.?|MSG=No MyBatis mapper was found in '[com.psbc.cpufp]' package. Please check your configuration.
********-16:10:11.421|INFO|null||***********:null|||7800|main|org.springframework.cloud.context.scope.GenericScope.?.?|MSG=BeanFactory id=9a40fb6f-7d7f-37ea-a956-0b63dff809a8
********-16:10:12.823|INFO|null||***********:null|||7800|main|org.springframework.boot.web.embedded.tomcat.TomcatWebServer.?.?|MSG=Tomcat initialized with port(s): 6666 (http)
********-16:10:12.855|INFO|null||***********:null|||7800|main|org.apache.coyote.http11.Http11NioProtocol.?.?|MSG=Initializing ProtocolHandler ["http-nio-6666"]
********-16:10:12.857|INFO|null||***********:null|||7800|main|org.apache.catalina.core.StandardService.?.?|MSG=Starting service [Tomcat]
********-16:10:12.857|INFO|null||***********:null|||7800|main|org.apache.catalina.core.StandardEngine.?.?|MSG=Starting Servlet engine: [Apache Tomcat/9.0.73]
********-16:10:13.373|INFO|null||***********:null|||7800|main|org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].?.?|MSG=Initializing Spring embedded WebApplicationContext
********-16:10:13.373|INFO|null||***********:null|||7800|main|org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.?.?|MSG=Root WebApplicationContext: initialization completed in 11308 ms
********-16:10:14.212|INFO|null||***********:null|||7800|main|org.springframework.boot.web.servlet.RegistrationBean.?.?|MSG=Servlet CXFServlet was not registered (possibly already registered?)
********-16:10:16.384|INFO|null||***********:6666|||7800|main|org.soma.job.client.SomaRpcProviderConfig.?.?|MSG=...SOMA>>>>>>>>>>>start
********-16:10:16.386|INFO|null||***********:6666|||7800|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init start after 0s
********-16:10:16.387|INFO|null||***********:6666|||7800|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>SomaProperties(initDelayTime=0, loggerAdapter=, remoteMaxThreadNum=3, remotePort=9944, execVersion=default, testFlag=0, coreAccessToken=, registryUrl=soma-kernel.cpufp-plat:20081, execAccessToken=b79558f7390c45d8bec48aa03fabaaa7, isAutoRegistry=1, execGroupId=EIDbfc5af50-005c-11ee-883c-fa163e7876be, env=default, isOpenDefaultLog=0)
********-16:10:16.940|INFO|null||***********:6666|||7800|main|org.apache.cxf.wsdl.service.factory.ReflectionServiceFactoryBean.?.?|MSG=Creating Service {http://impl.service.cpufp.psbc.com/}IWebServiceService from class com.psbc.cpufp.service.IWebService
********-16:10:18.101|ERROR|null||***********:6666|||7800|main|org.apache.cxf.transport.http.HTTPTransportFactory.?.?|MSG=Cannot find any registered HttpDestinationFactory from the Bus.
********-16:10:18.108|WARN|null||***********:6666|||7800|main|org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext.?.?|MSG=Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'endpoint' defined in class path resource [com/psbc/cpufp/config/WebServiceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [javax.xml.ws.Endpoint]: Factory method 'endpoint' threw exception; nested exception is javax.xml.ws.WebServiceException: org.apache.cxf.service.factory.ServiceConstructionException
********-16:10:18.165|INFO|null||***********:6666|||7800|main|org.apache.catalina.core.StandardService.?.?|MSG=Stopping service [Tomcat]
********-16:10:18.202|INFO|null||***********:6666|||7800|main|org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.?.?|MSG=

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
********-16:10:18.257|ERROR|null||***********:6666|||7800|main|org.springframework.boot.SpringApplication.?.?|MSG=Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'endpoint' defined in class path resource [com/psbc/cpufp/config/WebServiceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [javax.xml.ws.Endpoint]: Factory method 'endpoint' threw exception; nested exception is javax.xml.ws.WebServiceException: org.apache.cxf.service.factory.ServiceConstructionException
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:486)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:920)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.psbc.cpufp.BranchAgentApplication.main(BranchAgentApplication.java:16)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [javax.xml.ws.Endpoint]: Factory method 'endpoint' threw exception; nested exception is javax.xml.ws.WebServiceException: org.apache.cxf.service.factory.ServiceConstructionException
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 19 common frames omitted
Caused by: javax.xml.ws.WebServiceException: org.apache.cxf.service.factory.ServiceConstructionException
	at org.apache.cxf.jaxws.EndpointImpl.doPublish(EndpointImpl.java:373)
	at org.apache.cxf.jaxws.EndpointImpl.publish(EndpointImpl.java:255)
	at com.psbc.cpufp.config.WebServiceConfig.endpoint(WebServiceConfig.java:45)
	at com.psbc.cpufp.config.WebServiceConfig$$EnhancerBySpringCGLIB$$3e789ef4.CGLIB$endpoint$0(<generated>)
	at com.psbc.cpufp.config.WebServiceConfig$$EnhancerBySpringCGLIB$$3e789ef4$$FastClassBySpringCGLIB$$f3218770.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.psbc.cpufp.config.WebServiceConfig$$EnhancerBySpringCGLIB$$3e789ef4.endpoint(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 20 common frames omitted
Caused by: org.apache.cxf.service.factory.ServiceConstructionException: null
	at org.apache.cxf.frontend.ServerFactoryBean.create(ServerFactoryBean.java:193)
	at org.apache.cxf.jaxws.JaxWsServerFactoryBean.create(JaxWsServerFactoryBean.java:211)
	at org.apache.cxf.jaxws.EndpointImpl.getServer(EndpointImpl.java:458)
	at org.apache.cxf.jaxws.EndpointImpl.doPublish(EndpointImpl.java:336)
	... 32 common frames omitted
Caused by: java.io.IOException: Cannot find any registered HttpDestinationFactory from the Bus.
	at org.apache.cxf.transport.http.HTTPTransportFactory.getDestination(HTTPTransportFactory.java:276)
	at org.apache.cxf.binding.soap.SoapTransportFactory.getDestination(SoapTransportFactory.java:135)
	at org.apache.cxf.endpoint.ServerImpl.initDestination(ServerImpl.java:86)
	at org.apache.cxf.endpoint.ServerImpl.<init>(ServerImpl.java:65)
	at org.apache.cxf.frontend.ServerFactoryBean.create(ServerFactoryBean.java:182)
	... 35 common frames omitted
