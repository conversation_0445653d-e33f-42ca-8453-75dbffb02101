//package com.psbc.cpufp.entity.enu.generatekeyenum;
//
//import com.psbc.cpufp.common.cache.service.RedisKeyInterface;
//import com.psbc.cpufp.common.cache.service.TableInfoInterface;
//import com.psbc.cpufp.constant.KeyModelConstant;
//import com.psbc.cpufp.entity.enu.tableinfoenum.FrontTxCodeInfo4RedisEnum;
//import com.psbc.cpufp.entity.enu.tableinfoenum.TxCodeMapInfo4RedisEnum;
//
///**
// * 连表查询枚举:使用表：tb_front_txcode_info字段front_txcode和tb_txcode_map_info字段outsys_txcode_id联合查询
// *
// * <AUTHOR>
// */
//public enum TbFrontCodeUniteMapInfo4RedisEnum implements RedisKeyInterface {
//    /**
//     * 联合查询枚举
//     */
//    FRONT_TX_CODE_OUT_SYS_TX_CODE_ID_20230809(KeyModelConstant.KEY_TYPE_DB, "20230809",
//            FrontTxCodeInfo4RedisEnum.FRONT_TX_CODE,
//            TxCodeMapInfo4RedisEnum.OUT_SYS_TX_CODE_ID);
//
//    TbFrontCodeUniteMapInfo4RedisEnum(String type, String version, TableInfoInterface... tableFields) {
//        this.type = type;
//        this.tableFields = tableFields;
//        this.version = version;
//    }
//
//    /**
//     * 字段名
//     */
//    private TableInfoInterface[] tableFields;
//
//    /**
//     * 版本号
//     */
//    private String version;
//    /**
//     * 类型
//     */
//    private String type;
//    @Override
//    public String getVersion() {
//        return version;
//    }
//
//    @Override
//    public String getType() {
//        return type;
//    }
//
//    @Override
//    public TableInfoInterface[] getTableFields() {
//        return tableFields;
//    }
//}
