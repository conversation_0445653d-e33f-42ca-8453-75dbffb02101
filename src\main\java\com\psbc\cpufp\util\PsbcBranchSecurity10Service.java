//package com.psbc.cpufp.util;
//
//import com.alibaba.fastjson.JSONObject;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpHeaders;
//import org.springframework.stereotype.Service;
//
///**
// *安全认证10服务类
// *
// */
//@Slf4j
//@Service
//public class PsbcBranchSecurity10Service {
//
//    private static final Logger log = LoggerFactory.getLogger(PsbcBranchSecurity10Service.class);
//
//    /**
//    *分行前置请求公共参数
//    */
//    @Data
//    public static class PsbcBranchRequestComm   {
//        public String getReqSysCode()   {
//            return reqSysCode;
//        }
//
//        public void setReqSysCode(String reqSysCode)   {
//            this.reqSysCode = reqSysCode;
//        }
//
//        public String getUserCertSn()   {
//            return userCertSn;
//        }
//
//        public void setUserCertSn(String userCertSn)   {
//            this.userCertSn = userCertSn;
//        }
//
//        public String getUserCertPublicKey()   {
//            return userCertPublicKey;
//        }
//
//        public void setUserCertPublicKey(String userCertPublicKey)   {
//            this.userCertPublicKey = userCertPublicKey;
//        }
//
//        public String getUserCertPrivateKey()   {
//            return userCertPrivateKey;
//        }
//
//        public void setUserCertPrivateKey(String userCertPrivateKey)   {
//            this.userCertPrivateKey = userCertPrivateKey;
//        }
//
//        public String getBankCertSn()   {
//            return bankCertSn;
//        }
//
//        public void setBankCertSn(String bankCertSn)   {
//            this.bankCertSn = bankCertSn;
//        }
//
//        public String getBranchCertPubKey()   {
//            return branchCertPubKey;
//        }
//
//        public void setBranchCertPubKey(String branchCertPubKey)   {
//            this.branchCertPubKey = branchCertPubKey;
//        }
//
//        /**
//         * 请求方系统号
//         */
//        private String reqSysCode;
//
//        /**
//         * 请求方证书序列号
//         */
//        private String userCertSn;
//
//        /**
//         * 请求方证书公钥
//         */
//        private String userCertPublicKey;
//
//        /**
//         * 请求方证书私钥
//         */
//        private String userCertPrivateKey;
//
//        /**
//         * 服务方证书序列号
//         */
//        private String bankCertSn;
//
//        /**
//         * 服务方证书公钥
//         */
//        private String branchCertPubKey;
//    }
//
//    /**
//     *分行前置响应公共参数
//     */
//    @Data
//    public static class PsbcBranchResponseComm {
//
//        public String getBranchCertPubKey()   {
//            return branchCertPubKey;
//        }
//
//        public  void setBranchCertPubKey(String branchCertPubKey)   {
//            this.branchCertPubKey = branchCertPubKey;
//        }
//
//        public String getSign()   {
//            return sign;
//        }
//
//        public void setSign(String sign)   {
//            this.sign = sign;
//        }
//
//        public String getSm4Key()   {
//            return sm4Key;
//        }
//
//        public void setSm4Key(String sm4Key)   {
//            this.sm4Key = sm4Key;
//        }
//
//        public String getSysTrackNo()   {
//            return sysTrackNo;
//        }
//
//        public void setSysTrackNo(String sysTrackNo)   {
//            this.sysTrackNo = sysTrackNo;
//        }
//
//        public String getEncData()   {
//            return encData;
//        }
//
//        public void setEncData(String encData)   {
//            this.encData = encData;
//        }
//
//        public String getRespCode()   {
//            return respCode;
//        }
//
//        public void setRespCode(String respCode)   {
//            this.respCode = respCode;
//        }
//
//        public String getRespDesc()   {
//            return respDesc;
//        }
//
//        public void setRespDesc(String respDesc)   {
//            this.respDesc = respDesc;
//        }
//
//        /**
//        *服务方证书公钥
//        * */
//        private String branchCertPubKey;
//
//        /**
//        *加签后的签名值
//         */
//        private String sign;
//
//        /**
//        *加签后的签名值
//         */
//        private String sm4Key;
//
//        /**
//        *业务跟踪号
//         * */
//        private String sysTrackNo;
//
//        /**
//        *响应报文密文
//         * */
//        private String encData;
//
//        /**
//        * 响应码
//                */
//        private String respCode;
//
//        /**
//        * 响应描述
//         * */
//        private String respDesc;
//
//    }
//
//    /**
//        *报文字段:txComm
//        */
//    private static final String TX_COMM = "txComm";
//
//    /**
//        *报文字段:encData
//        */
//    private static final String ENC_DATA = "encData";
//
//    /**
//        *银行证书公钥
//        */
//    @Value("${jyt.bankCert.bankCertPublicKey}")
//    private String branchCertPubKey;
//
//    /**
//         * 银行证书序列号
//        */
//    @Value("${jyt.bankCert.bankCertSn}")
//    private String bankCertSn;
//
//    /**
//        *请求方系统号
//        */
//    @Value("${jyt.reqSysCode}")
//    private String regSysCode;
//
//    /**
//        *请求方用户证书序列号
//        */
//    @Value("${jyt.userCert.userCertSn}")
//    private String userCertSn;
//
//    /**
//        *请求方证书公钥
//         */
//    @Value("${jyt.userCert.userCertPublicKey}")
//    private String userCertPublicKey;
//
//    /**
//         * 请求方证书私钥
//         * */
//    @Value("${jyt.userCert.userCertPrivateKey}")
//    private String userCertPrivateKey;
//
//    /**
//         *安全认证10-请求调用
//     *
//         * @param requestBodyJson 请求业务报文明文
//         * @param requestHeaders  请求header
//         * @return 返回加密后的报文
//         */
//    public JSONObject psbcSecurity10Request(JSONObject requestBodyJson, HttpHeaders requestHeaders) throws Exception   {
//        //分行前置请求Header公共参数
//        PsbcBranchRequestComm headerTxComm = new PsbcBranchRequestComm();
//        //银行证书序列号
//        headerTxComm.setBankCertSn(bankCertSn);
//        //银行证书公钥
//        headerTxComm.setBranchCertPubKey(branchCertPubKey);
//        //请求方系统号
//        headerTxComm.setReqSysCode(regSysCode);
//        //请求方用户证书序列号
//        headerTxComm.setUserCertSn(userCertSn);
//        //请求方证书公钥
//        headerTxComm.setUserCertPublicKey(userCertPublicKey);
//        //请求方证书私钥
//        headerTxComm.setUserCertPrivateKey(userCertPrivateKey);
//        try   {
//            //安全认证10 加密，加签业务数据
//            return PsbcBranchSecurity10Util.branchSecurity10Request(requestBodyJson, headerTxComm, requestHeaders);
//        } catch (Exception e) {
//            throw new Exception("安全认证10加密加签异常", e);
//        }
//    }
//
//    /**
//     * 安全认证10-响应调用
//     * 响应报文
//     *
//     * @param responseBodyJson 响应
//     * @param psbcBranchResponseComm 响应公共参数
//     * @return 响应明文
//     * */
//    public String psbcSecurity10Response(JSONObject responseBodyJson, PsbcBranchResponseComm psbcBranchResponseComm)   {
//        if (responseBodyJson.get(TX_COMM) == null)   {
//            log.error("响应报文中缺少: {}字段", TX_COMM);
//            throw new RuntimeException("响应报文中缺少txComm字段");
//        }
//        String encData = responseBodyJson.getJSONObject(TX_COMM).getString(ENC_DATA);
//        if (StringUtils.isBlank(encData))   {
//            log.error("响应报文中缺少: {}字段", ENC_DATA);
//            throw new RuntimeException("响应报文中缺少encData字段");
//        }
//        //添加服务方证书公钥
//        psbcBranchResponseComm.setBranchCertPubKey(branchCertPubKey);
//        //添加待解密数据
//        psbcBranchResponseComm.setEncData(encData);
//        try   {
//            return PsbcBranchSecurity10Util.branchSecurity10Response(psbcBranchResponseComm);
//        } catch (Exception e)   {
//            throw new RuntimeException("响应报文解密失败", e);
//        }
//    }
//}