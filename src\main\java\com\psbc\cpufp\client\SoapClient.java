package com.psbc.cpufp.client;

import com.psbc.cpufp.service.UserService;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;


public class SoapClient {
  public static void main(String[] args) {
    JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
    factory.setServiceClass(UserService.class);
    factory.setAddress("http://localhost:8080/services/userService");
    UserService client = (UserService) factory.create();
    
    System.out.println(client.getUserById(1));
    client.listUsers().forEach(System.out::println);
  }
}
