<?xml version='1.0' encoding='UTF-8'?>
<BugCollection version='3.1.10' threshold='medium' effort='default'><file classname='com.psbc.cpufp.httpApplication.MyHttpHandler'><BugInstance type='DLS_DEAD_LOCAL_STORE' priority='Normal' category='STYLE' message='Dead store to headers in com.psbc.cpufp.httpApplication.MyHttpHandler.getRequestHeader(HttpExchange)' lineNumber='194'/><BugInstance type='DM_DEFAULT_ENCODING' priority='High' category='I18N' message='Found reliance on default encoding in com.psbc.cpufp.httpApplication.MyHttpHandler.sendZhbg(String, String): new java.io.InputStreamReader(InputStream)' lineNumber='121'/><BugInstance type='DM_DEFAULT_ENCODING' priority='High' category='I18N' message='Found reliance on default encoding in com.psbc.cpufp.httpApplication.MyHttpHandler.sendZhbg(String, String): String.getBytes()' lineNumber='125'/><BugInstance type='OS_OPEN_STREAM' priority='Normal' category='BAD_PRACTICE' message='com.psbc.cpufp.httpApplication.MyHttpHandler.getRequestParam(HttpExchange) may fail to close stream' lineNumber='230'/></file><file classname='com.psbc.cpufp.service.impl.WebServiceImpl'><BugInstance type='NM_METHOD_NAMING_CONVENTION' priority='Normal' category='BAD_PRACTICE' message='The method name com.psbc.cpufp.service.impl.WebServiceImpl.Execute(String, String) doesn&apos;t start with a lower case letter' lineNumber='30'/></file><file classname='com.psbc.cpufp.socket.PayServerSocket'><BugInstance type='NP_NULL_ON_SOME_PATH' priority='High' category='CORRECTNESS' message='Possible null pointer dereference of port in new com.psbc.cpufp.socket.PayServerSocket()' lineNumber='28'/></file><file classname='com.psbc.cpufp.socket.ServerThread'><BugInstance type='DM_DEFAULT_ENCODING' priority='High' category='I18N' message='Found reliance on default encoding in com.psbc.cpufp.socket.ServerThread.run(): new String(byte[])' lineNumber='200'/><BugInstance type='DM_DEFAULT_ENCODING' priority='High' category='I18N' message='Found reliance on default encoding in com.psbc.cpufp.socket.ServerThread.sendZhbg(String, String): new java.io.InputStreamReader(InputStream)' lineNumber='115'/><BugInstance type='DM_DEFAULT_ENCODING' priority='High' category='I18N' message='Found reliance on default encoding in com.psbc.cpufp.socket.ServerThread.sendZhbg(String, String): String.getBytes()' lineNumber='119'/><BugInstance type='RR_NOT_CHECKED' priority='Normal' category='BAD_PRACTICE' message='com.psbc.cpufp.socket.ServerThread.run() ignores result of java.io.DataInputStream.read(byte[])' lineNumber='199'/></file><file classname='com.psbc.cpufp.socket.SocketListener$MyHandler'><BugInstance type='DM_DEFAULT_ENCODING' priority='High' category='I18N' message='Found reliance on default encoding in com.psbc.cpufp.socket.SocketListener$MyHandler.handle(HttpExchange): String.getBytes()' lineNumber='74'/></file><file classname='com.psbc.cpufp.socket.SocketListener$MyHandler2'><BugInstance type='DM_DEFAULT_ENCODING' priority='High' category='I18N' message='Found reliance on default encoding in com.psbc.cpufp.socket.SocketListener$MyHandler2.handle(HttpExchange): String.getBytes()' lineNumber='90'/></file><file classname='com.psbc.cpufp.util.Wlhost'><BugInstance type='DM_DEFAULT_ENCODING' priority='High' category='I18N' message='Found reliance on default encoding in com.psbc.cpufp.util.Wlhost.sendToWl(String, int, String): new String(byte[])' lineNumber='32'/><BugInstance type='DM_DEFAULT_ENCODING' priority='High' category='I18N' message='Found reliance on default encoding in com.psbc.cpufp.util.Wlhost.sendToWl(String, int, String): String.getBytes()' lineNumber='26'/></file><Error></Error><Project><SrcDir>C:\workspace\bank\branch-agent-xm0628\src\main\java</SrcDir><SrcDir>C:\workspace\bank\branch-agent-xm0628\target\generated-sources\annotations</SrcDir><SrcDir>C:\workspace\bank\branch-agent-xm0628\src\test\java</SrcDir><SrcDir>C:\workspace\bank\branch-agent-xm0628\target\generated-test-sources\test-annotations</SrcDir></Project></BugCollection>