//package com.psbc.cpufp.service.impl;
//
//import com.psbc.cpufp.entity.jyt.JytInterLog;
//import com.psbc.cpufp.mapper.jyt.JytInterLogMapper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
///**
// * 接口日志记录
// *
// * <AUTHOR>
// */
//@Service
//public class JytInterLogServiceImpl {
//    /**
//     * mapper
//     */
//    @Autowired
//    private JytInterLogMapper jytInterLogMapper;
//
//    public boolean insertLog(JytInterLog jytInterLog) {
//        return jytInterLogMapper.insertIntoJytLog(jytInterLog);
//    }
//
//
//}
