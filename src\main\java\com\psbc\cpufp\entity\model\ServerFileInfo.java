//package com.psbc.cpufp.entity.model;
//
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.util.Map;
//
///**
// * ServerFileInfo
// */
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//public class ServerFileInfo {
//    /**
//     * 文件路径 （包含文件名）
//     */
//    private String filePath;
//
//    /**
//     * 文件名称
//     */
//    private String fileName;
//
//    /**
//     * 扩展信息
//     */
//    private Map<String, Object> extendInfo;
//}
