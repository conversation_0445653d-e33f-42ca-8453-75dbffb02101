//package com.psbc.cpufp.entity.enu.tableinfoenum;
//
//import com.psbc.cpufp.common.cache.service.TableInfoInterface;
//
///**
// * 表枚举，每个表对应一个
// *
// * <AUTHOR>
// */
//public enum FrontTxCodeInfo4RedisEnum implements TableInfoInterface {
//    /**
//     * front_txcode
//     */
//    FRONT_TX_CODE("front_txcode"),
//    /**
//     * flag
//     */
//    FLAG("flag");
//
//    FrontTxCodeInfo4RedisEnum(String frontTxCode) {
//        this.tableFields = frontTxCode;
//    }
//
//    /**
//     * 字段名
//     */
//    private String tableFields;
//
//    @Override
//    public String getTableField() {
//        return tableFields;
//    }
//
//    public String getTableName() {
//        // 表名\引用常量或者写变量
//        return "tb_front_txcode_info";
//    }
//}
