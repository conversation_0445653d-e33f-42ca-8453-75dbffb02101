apiVersion: apps/v1
kind: Deployment
metadata:
  name:  branch-agent
  namespace: {{ .Values.nameSpace }}
spec:
  replicas: {{ .Values.appreplicaCount }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app:  branch-agent
      release: {{ .Values.release | quote }}
  template:
    metadata:
      labels:
        app:  branch-agent
        release: {{ .Values.release | quote }}
        env: {{ .Values.labels.env }}
    spec:
      # 配置反亲和性，确保副本分配到不同的主机
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  -  branch-agent
              topologyKey: "kubernetes.io/hostname" 
      # pod 停止宽限期
      terminationGracePeriodSeconds: 60
      containers:
      - name:  branch-agent
        image: "{{ .Values.image.imageName }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        command: ["/bin/bash", "-c", "sh install.sh branch-agent {{ .Values.labels.env }}"]
        #command: ["/bin/bash", "-c", "while true;do echo $(date);sleep 60;done;"]
        ports:
        - name: branch-agent
          containerPort: {{ .Values.portResources.branchAgent }}
        livenessProbe:
          httpGet:
            port: {{ .Values.portResources.branchAgent }}
            path: /actuator/health
            scheme: HTTP
          initialDelaySeconds: 30
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 5
        readinessProbe:
          httpGet:
            port: {{ .Values.portResources.branchAgent }}
            path: /actuator/health
            scheme: HTTP
          initialDelaySeconds: 30
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh","-c","ps -ef | grep java | grep -v grep | awk '{print $2}' | xargs kill -15"]
        volumeMounts:
        - name: timezone
          mountPath: /etc/localtime
        - mountPath: {{ .Values.mountDir.containerLogDir.branchAgent }}
          name: log
        envFrom: 
        - configMapRef: 
            name: branch-agent
        - configMapRef:
            name: db-config-biz
        - configMapRef:
            name: redis-config-biz
        - configMapRef:
            name: fastdfs-config-biz
        env:
          - name: cpufp_pod_ip
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
      volumes:
      - name: timezone
        hostPath:
          path: {{ .Values.timezone }}
      - name: log
        hostPath:
          path: {{ .Values.mountDir.hostLogDir.branchAgent }}
          type: {{ .Values.mountDir.hostPathType }}
      dnsPolicy: ClusterFirst
      dnsConfig:
        options:
          - name: single-request-reopen

---

apiVersion: v1
kind: Service
metadata:
  name:  branch-agent
  namespace: {{ .Values.nameSpace }}
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: {{ .Values.portResources.branchAgent | quote }}
    prometheus.io/path: "/actuator/prometheus"
spec:
  type: ClusterIP
  selector:  # bind the deployment
    app:  branch-agent
    release: {{ .Values.release | quote }}
  ports:
  - name:  branch-agent
    port: {{ .Values.portResources.branchAgent }}
    targetPort: {{ .Values.portResources.branchAgent }}
