//package com.psbc.cpufp.service.impl;
//
//import com.psbc.cpufp.common.cache.redis.RedisCacheUtil;
//import com.psbc.cpufp.common.cache.service.TableInfoInterface;
//import com.psbc.cpufp.common.cache.utils.FuzzyKeyOfTableInfoUtil;
//import com.psbc.cpufp.entity.enu.tableinfoenum.FrontTxCodeInfo4RedisEnum;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.HashMap;
//
///**
// * FrontTxCodeInfo4RedisServerImpl
// */
//@Service
//public class FrontTxCodeInfo4RedisServerImpl {
//
//    /**
//     * Redis查询操作
//     */
//    @Autowired
//    private RedisCacheUtil redisCacheUtil;
//
//    /**
//     * Redis删除
//     */
//    @Autowired
//    private FuzzyKeyOfTableInfoUtil fuzzyKeyOfTableInfoUtil;
//
//
//    /**
//     * 获取FrontTxCodeInfo
//     *
//     * @param frontTxCode 前置交易码
//     * @param flag        启用标识
//     */
//    public void delete(String frontTxCode, String flag) {
//        // 构造RedisKey的数据
//        HashMap<TableInfoInterface, Object> tableEnmObjectHashMap = new HashMap<>();
//        tableEnmObjectHashMap.put(FrontTxCodeInfo4RedisEnum.FLAG, flag);
//        tableEnmObjectHashMap.put(FrontTxCodeInfo4RedisEnum.FRONT_TX_CODE, frontTxCode);
//
//        // 获取数据
//        fuzzyKeyOfTableInfoUtil.delete("tb_front_txcode_info", tableEnmObjectHashMap);
//    }
//}
