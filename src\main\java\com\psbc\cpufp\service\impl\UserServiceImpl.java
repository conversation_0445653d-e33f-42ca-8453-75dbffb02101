package com.psbc.cpufp.service.impl;

import com.psbc.cpufp.entity.vo.UserDto;
import com.psbc.cpufp.service.UserService;
import org.springframework.stereotype.Service;

import javax.jws.WebService;
import java.util.Arrays;
import java.util.List;

@WebService(
        endpointInterface="com.psbc.cpufp.service.UserService",
        serviceName="UserService",
        targetNamespace="http://webservice.example.com/"
)
@Service
public class UserServiceImpl implements UserService {
  @Override
  public UserDto getUserById(Integer id) {
    return new UserDto(id, "张三", "pass123", 1);
  }

  @Override
  public List<UserDto> listUsers() {
    return Arrays.asList(
            new UserDto(1, "张三", "pass123", 1),
            new UserDto(2, "李四", "pass456", 0)
    );
  }
}
