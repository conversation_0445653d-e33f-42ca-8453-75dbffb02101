//package com.psbc.cpufp.service.impl;
//
//import org.springframework.stereotype.Service;
//
///**
// * 响应码转换
// **/
//@Service
//public class ResponseModifyServiceImpl {
//
////    private ChangeResponseCodeAndDescService changeResponseCodeAndDescService;
////
////    /**
////     * 目的系统响应码转前置响应码
////     *
////     * @param endPoint     接口标识
////     * @param originalBody 响应报文
////     * @return 转换后的结果
////     */
////    public ChangedResult convertRespCodeDest2Cpufp(String endPoint, String originalBody) {
////        return changeResponseCodeAndDescService.convertRespCodeDest2Cpufp(endPoint, originalBody);
////    }
////
////    /**
////     * 前置映射源系统响应码
////     *
////     * @param reqSysCode 接入系统
////     * @param respCode   前置响应码
////     * @param respDesc   前置响应描述
////     * @return 映射后的响应码响应描述
////     */
////    public ChangedResult convertRespCodeCpufp2SourceSys(String reqSysCode, String respCode, String respDesc) {
////        return changeResponseCodeAndDescService.convertRespCodeCpufp2SourceSys(reqSysCode, respCode, respDesc);
////    }
////
////    /**
////     * 目的系统响应码转前置响应码
////     *
////     * @param endPoint     接口标识
////     * @param originalBody 响应报文
////     * @return 转换后的结果
////     */
////    public ChangedResult convertRespCodeDest2SourceSys(String reqSysCode, String endPoint, String originalBody) {
////        ChangedResult changedResult = changeResponseCodeAndDescService.convertRespCodeDest2Cpufp(endPoint, originalBody);
////        if (changedResult == null || !ChangeResultEnum.CHANGE_SUCCESS.getCode().equals(changedResult.getResult().getCode())) {
////            return changedResult;
////        }
////        return this.convertRespCodeCpufp2SourceSys(reqSysCode, changedResult.getChangedRespCode(), changedResult.getChangedRespDesc());
////    }
//}
