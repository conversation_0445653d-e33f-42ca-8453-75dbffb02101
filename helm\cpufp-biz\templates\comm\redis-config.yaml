apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config-biz
  namespace: {{ .Values.nameSpace }}
data:
  {{- with .Values.comm.redis }}
  {{- if eq .mode "cluster" }}
  spring_redis_cluster_nodes: {{ .host | quote }}
  {{- else }}
  spring_redis_host: {{ .host | quote }}
  spring_redis_port: {{ .port | quote }}
  {{- end }}
  spring_redis_password: {{ .password | quote }}
  {{- end }}
  spring_redis_timeout: "2000"
  spring_redis_database: "1"
  spring_redis_jedis_pool_max_active: "50"
  spring_redis_jedis_pool_max_wait: "-1"
  spring_redis_jedis_pool_max_idle: "50"
  spring_redis_jedis_pool_min_idle: "1"
  cpufp_cache_util_timeout: "600"
