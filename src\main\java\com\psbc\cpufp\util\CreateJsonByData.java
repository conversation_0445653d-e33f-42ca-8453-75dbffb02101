//package com.psbc.cpufp.util;
//
//import com.psbc.cpufp.entity.jyt.JytInterConfMaintence;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Objects;
//import java.util.concurrent.atomic.AtomicReference;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> Ye
// * @version 1.0.0
// * @title CreateJsonByData
// * @description 根据数据组装数据格式
// * @create 2025/4/14 20:32
// **/
//@Component
//public class CreateJsonByData {
//
//    /**
//     * json 转 数据
//     *
//     * @param jytInterConfMaintences 配置对象
//     * @return 字符
//     */
//    public String createJsonByData(List<JytInterConfMaintence> jytInterConfMaintences) {
//        // List<JytInterConfMaintence> jytInterConfMaintences = jytInterConfMaintenceMapper.selectList(new LambdaQueryWrapper<>());
//        if (jytInterConfMaintences.isEmpty()) {
//            throw new RuntimeException("未查找到数据");
//        }
//        return combinationStr(streamToTree(jytInterConfMaintences, "0")).replace(",}", "}");
//    }
//
//    /**
//     * list 转 树
//     *
//     * @param treeList 树集合
//     * @param parentId 父id
//     * @return 树形结构
//     */
//    public List<JytInterConfMaintence> streamToTree(List<JytInterConfMaintence> treeList, String parentId) {
//        return treeList.stream()
//                // 过滤父节点
//                .filter(parent -> parent.getMsgUpNode().equals(parentId))
//                // 把父节点children递归赋值成为子节点
//                .peek(child -> child.setChildren(streamToTree(treeList, child.getId()))).collect(Collectors.toList());
//    }
//
//
//    /**
//     * 组装
//     *
//     * @param list 集合
//     * @return 结果
//     */
//    public String combinationStr(List<JytInterConfMaintence> list) {
//        AtomicReference<String> str = new AtomicReference<>("{");
//        list.forEach(item -> {
//            str.set(str + "\"" + item.getMsgNodeCode() + "\":" + judgeChild(item, item.getNodeValue()) + ",");
//        });
//        str.set(str + "}");
//        return str.get();
//    }
//
//    /**
//     * 判断是否有子集
//     *
//     * @param item      对象
//     * @param nodeValue 节点值
//     * @return 结果
//     */
//    private String judgeChild(JytInterConfMaintence item, String nodeValue) {
//        if (!item.getChildren().isEmpty()) {
//            return this.combinationStr(item.getChildren());
//        } else {
//            return Objects.equals(nodeValue, "{}") ? nodeValue : '"' + (nodeValue == null ? "" : nodeValue) + '"';
//        }
//    }
//
//
//}
