package com.psbc.cpufp.config;

import com.psbc.cpufp.service.IWebService;
import com.psbc.cpufp.service.impl.UserServiceImpl;
import org.apache.cxf.Bus;
import org.apache.cxf.bus.spring.SpringBus;
import org.apache.cxf.jaxws.EndpointImpl;
import org.apache.cxf.transport.servlet.CXFServlet;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Value;

import javax.xml.ws.Endpoint;

@Configuration
public class WebServiceConfig {

    @Value("${xmysfzjjg.IP: }")
    private String serverIp;

    @Value("${xmysfzjjg.PORT:9702}")
    private Integer serverPort;

    @Bean
    public ServletRegistrationBean<CXFServlet> cxfServlet() {
        return new ServletRegistrationBean<>(new CXFServlet(), "/xmysfzjjg/wservices/*");
    }

    @Bean(name = Bus.DEFAULT_BUS_ID)
    public SpringBus springBus() {
        return new SpringBus();
    }

    @Bean
    public IWebService webService() {
        return new com.psbc.cpufp.service.impl.WebServiceImpl();
    }

    @Bean
    public Endpoint endpoint() {
        String address = String.format("http://%s:%d/xmysfzjjg/wservices/IWebServiceService",
                serverIp, serverPort);
        EndpointImpl endpoint = new EndpointImpl(springBus(), webService());
        endpoint.publish(address);
        return endpoint;
    }
    // 发布到 /services/userService
    @Bean
    public Endpoint userServiceEndpoint(UserServiceImpl impl) {
        EndpointImpl endpoint = new EndpointImpl(springBus(), impl);
        endpoint.publish("/userService");
        return endpoint;
    }
}