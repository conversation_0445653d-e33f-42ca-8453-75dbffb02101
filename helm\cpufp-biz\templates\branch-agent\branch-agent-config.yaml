apiVersion: v1
kind: ConfigMap
metadata:
  name: branch-agent
  namespace: {{ .Values.nameSpace }}
data:
  spring_datasource_maxWait: "10000"
  spring_datasource_minIdle: "1"
  spring_datasource_maxActive: "5"
  spring_datasource_initialSize: "1"
  spring_datasource_breakAfterAcquireFailure: "true"
  spring_datasource_connectionErrorRetryAttempts: "10"
  spring_datasource_timeBetweenEvictionRunsMillis: "10000"
  spring_datasource_minEvictableIdleTimeMillis: "300000"
  spring_datasource_validationQuery: "select 'x'"
  spring_datasource_testWhileIdle: "true"
  spring_datasource_testOnBorrow: "true"
  spring_datasource_testOnReturn: "false"
  spring_datasource_queryTimeout: "5"
  spring_datasource_removeAbandoned: "true"
  spring_datasource_removeAbandonedTimeout: "300"
  spring_datasource_filters: "stat,wall"
  spring_datasource_useGlobalDataSourceStat: "true"
  spring_datasource_connectionProperties: "druid.stat.mergeSql=true;druid.stat.slowSqlMillis=1000"
#  spring_datasource_one_maxWait: "10000"
#  spring_datasource_one_minIdle: "1"
#  spring_datasource_one_maxActive: "5"
#  spring_datasource_one_initialSize: "1"
#  spring_datasource_one_breakAfterAcquireFailure: "true"
#  spring_datasource_one_connectionErrorRetryAttempts: "10"
#  spring_datasource_one_timeBetweenEvictionRunsMillis: "10000"
#  spring_datasource_one_minEvictableIdleTimeMillis: "300000"
#  spring_datasource_one_validationQuery: "select 'x'"
#  spring_datasource_one_testWhileIdle: "true"
#  spring_datasource_one_testOnBorrow: "true"
#  spring_datasource_one_testOnReturn: "false"
#  spring_datasource_one_queryTimeout: "5"
#  spring_datasource_one_removeAbandoned: "true"
#  spring_datasource_one_removeAbandonedTimeout: "300"
#  spring_datasource_one_filters: "stat,wall"
#  spring_datasource_one_useGlobalDataSourceStat: "true"
#  spring_datasource_one_connectionProperties: "druid.stat.mergeSql=true;druid.stat.slowSqlMillis=1000"
#  spring_datasource_two_maxWait: "10000"
#  spring_datasource_two_minIdle: "1"
#  spring_datasource_two_maxActive: "5"
#  spring_datasource_two_initialSize: "1"
#  spring_datasource_two_breakAfterAcquireFailure: "true"
#  spring_datasource_two_connectionErrorRetryAttempts: "10"
#  spring_datasource_two_timeBetweenEvictionRunsMillis: "10000"
#  spring_datasource_two_minEvictableIdleTimeMillis: "300000"
#  spring_datasource_two_validationQuery: "select 'x'"
#  spring_datasource_two_testWhileIdle: "true"
#  spring_datasource_two_testOnBorrow: "true"
#  spring_datasource_two_testOnReturn: "false"
#  spring_datasource_two_queryTimeout: "1"
#  spring_datasource_two_removeAbandoned: "true"
#  spring_datasource_two_removeAbandonedTimeout: "5"
#  spring_datasource_two_filters: "stat,wall"
#  spring_datasource_two_useGlobalDataSourceStat: "true"
#  spring_datasource_two_connectionProperties: "druid.stat.mergeSql=true;druid.stat.slowSqlMillis=1000"
  enum_config.prefix: {{ .Values.comm.common.cpufpSystemNo7 | quote }}
  soma_id: {{ .Values.branchAgent.soma_id | quote }}
  soma_token: {{ .Values.branchAgent.soma_token | quote }}
  cpufp_reqSysCode: {{ .Values.comm.common.cpufpSystemNo11 | quote }}
  cpufp_businessSendInstNo: "11111111"
  cpufp_clientSecret: {{ .Values.branchAgent.cpufpClientSecret | quote }}
  cpufp_startSysOrCmptNo: {{ .Values.comm.common.cpufpSysCode | quote }}
  cpufp_tokenRefreshEndPoint: "/gw/v1/600432"
  JASYPT_PASSWORD: {{ .Values.comm.common.jasyptPassword | quote }}
  management_endpoints_web_exposure_include: {{ .Values.comm.health.endpoints | quote }}
  #请求方系统号（警银通系统号）
  reqSysCode: ************
  #请求方用户证书序列号(警银通)
  userCertSn:
  #请求方证书公钥(警银通)
  userCertPublicKey: MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEOpKY2xrUvdKm1EJVA4X0I7mL8VLUGIEWoGUVI7S93WmaTuMsr8iiSZ7iDrueA0Cbb4dG09BznkKTXUoDnSGeJA==
  #请求方证书私钥(警银通)
  userCertPrivateKey: MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgUzUTq5ocwlLEDaLYemARJZ9j+h5MJyjASbju4E8SKMigCgYIKoEcz1UBgi2hRANCAAQ6kpjbGtS90qbUQlUDhfQjuYvxUtQYgRagZRUjtL3daZpO4yyvyKJJnuIOu54DQJtvh0bT0HOeQpNdSgOdIZ4k
  #银行证书公钥(分行前置)
  bankCertPublicKey: 04a9ab30feee114284a6155b30eae8426c794b2da2ecf15defb5f0d92c77617017d9c996574ef78666e4455e57aa86f2532ebc74a90d00e6c1cdafc646a8a108ea
  #银行证书序列号(分行前置)
  bankCertSn: 0195f5836b64
  #公安公钥
  gaCertPublicKey: MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAENWHYBmioTIPpAZFdccHqwgCDG2nTRtea/VkhPUOeBB725+IvI9rsKclWkObCRC1dadxYoF+wL+RXQrpVkWmnMA==
  #卡开户信息接口
  601087Url: "http://************:9902/gw/v1/trans/J601087"
  #反欺诈查询客户名下用户接口
  600368Url: "http://************:9902/gw/v1/trans/J600368"
  #止付交易接口
  600010Url: "http://************:9902/gw/v1/trans/J600010"