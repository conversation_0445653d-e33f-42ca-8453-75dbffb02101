//package com.psbc.cpufp.entity.jyt;
//
//import java.io.Serializable;
//
///**
// *@ClassName JytInterLog 警银通接口日志表 - jyt_inter_log
// *@Description 警银通接口日志表model
// *<AUTHOR>
// *@Date 2025-04-15 15：04
// *
// * */
//public class JytInterLog implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 主键
//     * 必填
//     */
//    private String logId;
//
//    /**
//     * 记录日期 yyyyMMdd
//     * 必填
//     */
//    private String txDate;
//
//    /**
//     * 记录时间 hhmiss
//     * 必填
//     */
//    private String txTime;
//
//
//    /**
//     * 请求方 GA-公安/FH-分行前置/ZH-总行前置
//     */
//    private String requester;
//
//    /**
//     * 接收方 FH/GA/ZH
//     */
//    private String responder;
//
//    /**
//     * 请求接口
//     */
//    private String reqInter;
//
//    /**
//     * 请求接口url
//     */
//    private String reqInterUrl;
//
//    /**
//     * 请求参数
//     */
//    private String reqParam;
//
//
//    /**
//     * 响应结果 0-成功/1-失败
//     * 必填
//     */
//    private String respRes;
//
//    /**
//     * 响应结果说明 失败原因
//     * 失败必填
//     */
//    private String respDesc;
//
//    /**
//     * 附件地址 请求过程中产生的文件存放的地址
//     */
//    private String attrAddr;
//
//    /**
//     * 备选字段 oth_msg_1_oth_msg_8
//     */
//    private String othMsg1;
//
//    /**
//     * 备选字段 oth_msg_1_oth_msg_8
//     */
//    private String othMsg2;
//
//    /**
//     * 备选字段 oth_msg_1_oth_msg_8
//     */
//    private String othMsg3;
//
//    /**
//     * 备选字段 oth_msg_1_oth_msg_8
//     */
//    private String othMsg4;
//
//    /**
//     * 备选字段 oth_msg_1_oth_msg_8
//     */
//    private String othMsg5;
//
//    /**
//     * 备选字段 oth_msg_1_oth_msg_8
//     */
//    private String othMsg6;
//
//    /**
//     * 备选字段 oth_msg_1_oth_msg_8
//     */
//    private String othMsg7;
//
//    /**
//     * 备选字段 oth_msg_1_oth_msg_8
//     */
//    private String othMsg8;
//
//    public String getLogId() {
//        return logId;
//    }
//
//    public void setLogId(String logId) {
//        this.logId = logId;
//    }
//
//    public String getTxDate() {
//        return txDate;
//    }
//
//    public void setTxDate(String txDate) {
//        this.txDate = txDate;
//    }
//
//    public String getTxTime() {
//        return txTime;
//    }
//
//    public void setTxTime(String txTime) {
//        this.txTime = txTime;
//    }
//
//    public String getRequester() {
//        return requester;
//    }
//
//    public void setRequester(String requester) {
//        this.requester = requester;
//    }
//
//    public String getResponder() {
//        return responder;
//    }
//
//    public void setResponder(String responder) {
//        this.responder = responder;
//    }
//
//    public String getReqInter() {
//        return reqInter;
//    }
//
//    public void setReqInter(String reqInter) {
//        this.reqInter = reqInter;
//    }
//
//    public String getReqInterUrl() {
//        return reqInterUrl;
//    }
//
//    public void setReqInterUrl(String reqInterUrl) {
//        this.reqInterUrl = reqInterUrl;
//    }
//
//    public String getReqParam() {
//        return reqParam;
//    }
//
//    public void setReqParam(String reqParam) {
//        this.reqParam = reqParam;
//    }
//
//    public String getRespRes() {
//        return respRes;
//    }
//
//    public void setRespRes(String respRes) {
//        this.respRes = respRes;
//    }
//
//    public String getRespDesc() {
//        return respDesc;
//    }
//
//    public void setRespDesc(String respDesc) {
//        this.respDesc = respDesc;
//    }
//
//    public String getAttrAddr() {
//        return attrAddr;
//    }
//
//    public void setAttrAddr(String attrAddr) {
//        this.attrAddr = attrAddr;
//    }
//
//    public String getOthMsg1() {
//        return othMsg1;
//    }
//
//    public void setOthMsg1(String othMsg1) {
//        this.othMsg1 = othMsg1;
//    }
//
//    public String getOthMsg2() {
//        return othMsg2;
//    }
//
//    public void setOthMsg2(String othMsg2) {
//        this.othMsg2 = othMsg2;
//    }
//
//    public String getOthMsg3() {
//        return othMsg3;
//    }
//
//    public void setOthMsg3(String othMsg3) {
//        this.othMsg3 = othMsg3;
//    }
//
//    public String getOthMsg4() {
//        return othMsg4;
//    }
//
//    public void setOthMsg4(String othMsg4) {
//        this.othMsg4 = othMsg4;
//    }
//
//    public String getOthMsg5() {
//        return othMsg5;
//    }
//
//    public void setOthMsg5(String othMsg5) {
//        this.othMsg5 = othMsg5;
//    }
//
//    public String getOthMsg6() {
//        return othMsg6;
//    }
//
//    public void setOthMsg6(String othMsg6) {
//        this.othMsg6 = othMsg6;
//    }
//
//    public String getOthMsg7() {
//        return othMsg7;
//    }
//
//    public void setOthMsg7(String othMsg7) {
//        this.othMsg7 = othMsg7;
//    }
//
//    public String getOthMsg8() {
//        return othMsg8;
//    }
//
//    public void setOthMsg8(String othMsg8) {
//        this.othMsg8 = othMsg8;
//    }
//
//
//
//
//
//
//
//
//
//
//}
