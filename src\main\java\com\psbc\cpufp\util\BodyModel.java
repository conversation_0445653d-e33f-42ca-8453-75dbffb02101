//package com.psbc.cpufp.util;
//
//import com.fasterxml.jackson.annotation.JsonProperty;
//import lombok.Data;
//import org.springframework.stereotype.Service;
//
///**
// *
// * */
//@Data
//@Service
//public class BodyModel {
//
//
//    //    @ApiModelProperty(value = "经过Base64编码后的原文D")
//    @JsonProperty(value = "Content")
//    private String content;
//
//    //    @ApiModelProperty(value = "签名值C")
//    @JsonProperty(value = "SignatureValue")
//    private String signatureValue;
//
//    public String getContent() {
//        return content;
//    }
//
//    public void setContent(String content) {
//        this.content = content;
//    }
//
//    public String getSignatureValue() {
//        return signatureValue;
//    }
//
//    public void setSignatureValue(String signatureValue) {
//        this.signatureValue = signatureValue;
//    }
//
//
//}
