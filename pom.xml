<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.psbc.cpufp.bfes</groupId>
        <artifactId>cpufp-bfes-dependencies</artifactId>
        <version>0.0.7.RELEASE</version>
    </parent>
    <groupId>com.psbc.cpufp</groupId>
    <artifactId>branch-agent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <properties>
        <comm.log.frame.version>1.0.2.RELEASE</comm.log.frame.version>
        <common.jasypt.decrypt.version>1.0.2.RELEASE</common.jasypt.decrypt.version>
        <bfes-agent-comm-version>1.0.11.RELEASE</bfes-agent-comm-version>
        <pagehelper-spring-boot-starter.version>1.4.1</pagehelper-spring-boot-starter.version>
        <pagehelper.version>5.3.1</pagehelper.version>
        <common.serial.api.version>1.0.2.RELEASE</common.serial.api.version>
        <common.cache.client.version>1.0.7.RELEASE</common.cache.client.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.psbc.cpufp.bfes</groupId>
            <artifactId>bfes-agent-comm</artifactId>
            <version>${bfes-agent-comm-version}</version>
        </dependency>
        <!--        spring-->
        <!--        参数校验-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--日志框架-->
        <dependency>
            <groupId>com.psbc.cpufp.bfes</groupId>
            <artifactId>common.log.frame</artifactId>
            <version>${comm.log.frame.version}</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>checker-qual</artifactId>
                    <groupId>org.checkerframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <!--        加解密-->
        <dependency>
            <groupId>com.psbc.cpufp.bfes</groupId>
            <artifactId>common.jasypt.decrypt</artifactId>
            <version>${common.jasypt.decrypt.version}</version>
        </dependency>
        <dependency>
            <groupId>com.psbc.soma.job</groupId>
            <artifactId>soma_client_java</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>grpc-api</artifactId>
                    <groupId>io.grpc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>error_prone_annotations</artifactId>
                    <groupId>com.google.errorprone</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--pagehelper 分页 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${pagehelper-spring-boot-starter.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>com.psbc.cpufp.bfes</groupId>
            <artifactId>common.serial.api</artifactId>
            <version>${common.serial.api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.psbc.cpufp.bfes</groupId>
            <artifactId>common.cache.client</artifactId>
            <version>${common.cache.client.version}</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.psbc.cpufp.bfes</groupId>-->
        <!--            <artifactId>common-modify-response</artifactId>-->
        <!--            <version>1.0.6.RELEASE</version>-->
        <!--        </dependency>-->
<!--        新增的jar-->
        <!-- CXF Spring Integration -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <version>3.5.5</version>
        </dependency>

        <!-- CXF Runtime for JAX-WS -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-frontend-jaxws</artifactId>
            <version>3.5.5</version>
        </dependency>

        <!-- CXF Runtime for HTTP Transport -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-transports-http</artifactId>
            <version>3.5.5</version>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.4</version>
        </dependency>
        <dependency>
            <groupId>com.psbc</groupId>
            <artifactId>cpufp-security-sdk</artifactId>
            <version>1.0.6.RELEASE</version>
        </dependency>
    </dependencies>
    <!--构建配置-->
    <build>
        <!--产生的构建的文件名}-->
        <finalName>branch-agent</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
<!--            检查代码格式的插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>${checkstyle.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>${exec-maven-plugin.version}</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <configLocation>${project.basedir}/code-check-config/cpufp_checkStyle.xml
                    </configLocation>
                    <inputEncoding>${project.build.sourceEncoding}</inputEncoding>
                    <skip>true</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>checkstyle-check</id>
                        <phase>initialize</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <violationSeverity>warning</violationSeverity>
                            <failOnViolation>true</failOnViolation>
                            <failsOnError>true</failsOnError>
                            <consoleOutput>true</consoleOutput>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>${spotbugs-maven-plugin.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>com.github.spotbugs</groupId>
                        <artifactId>spotbugs</artifactId>
                        <version>${spotbugs.version}</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <xmlOutput>true</xmlOutput>
                    <excludeFilterFile>${project.basedir}/code-check-config/spotbugsExcludes.xml
                    </excludeFilterFile>
                    <includeFilterFile>${project.basedir}/code-check-config/sourceFiles.xml</includeFilterFile>
                </configuration>
                <executions>
                    <execution>
                        <id>exec-spotbugs</id>
                        <phase>package</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <failOnError>true</failOnError>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.ly.smart-doc</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>