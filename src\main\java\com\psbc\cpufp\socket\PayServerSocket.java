package com.psbc.cpufp.socket;

import com.psbc.cpufp.config.XmysfzjjgProperties;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;

@Slf4j
public class PayServerSocket extends Thread {
  
  private ServerSocket serverSocket;

 @Resource
  private XmysfzjjgProperties xmysfzjjgProperties;
  
  public PayServerSocket() {
    if (this.serverSocket == null) {
      Integer port = xmysfzjjgProperties.getWgPort();
      if (port == null) {
    	 log.error("读取socket服务端监听端口失败");
      } else {
    	 log.info("socket服务端监听端囗port:"+port);
      } 
      try {
        this.serverSocket = new ServerSocket(port);
        log.info("socket服务端启动成功");
      } catch (NumberFormatException e) {
    	log.info("socket服务端启动失败");
        e.printStackTrace();
      } catch (IOException e) {
    	log.info("socket服务端启动失败");
        e.printStackTrace();
      } 
    } 
  }
  
  public void stopserver() {
    try {
      if (!this.serverSocket.isClosed()) {
        this.serverSocket.close();
        log.info("socket服务端停止");
      } 
    } catch (IOException e) {
    	log.error("socket服务端停止异常",e);
    } 
  }
  
  public void run() {
    Socket socket = null;
    while (!this.serverSocket.isClosed()) {
      try {
        socket = this.serverSocket.accept();
        if (socket != null) {
        	log.error("新建socket处理线程开始>>>");
          (new ServerThread(socket)).start();
        } 
      } catch (IOException e) {
    	log.error("serversocket监听异常");
        e.printStackTrace();
      } catch (Exception e) {
    	log.error("serversocket监听异常");
        e.printStackTrace();
      } 
    } 
  }
}
