//package com.psbc.cpufp.mapper.jyt;
//
//import com.psbc.cpufp.entity.jyt.JytInterLog;
//import org.apache.ibatis.annotations.Mapper;
//
//
///**
// * <AUTHOR>
// */
//@Mapper
//public interface JytInterLogMapper {
//
//    //    /**
//    //     * 查询接口日志记录
//    //     *
//    //     * @param jytInterLog 参数
//    //     * @return 日志列表
//    //     */
//    //    List<JytInterLog> queryList(JytInterLog jytInterLog);
//
//    //    /**
//    //     * 查询日志详情
//    //     *
//    //     * @param id 参数
//    //     * @return 单条日志记录
//    //     */
//    //    JytInterLog selectInfoById(@Param("id") String id);
//
//    /**
//     * 往数据库插入一条日志
//     *
//     * @param jytInterLog 参数
//     * @return 单条日志记录
//     */
//    boolean insertIntoJytLog(JytInterLog jytInterLog);
//
//
//    //    Map<String, String> selectUniteMultiple(@Param("frontTxCode") String frontTxCode, @Param("flag") String flag);
//}
