//package com.psbc.cpufp.controller;
//
//import cn.hutool.core.net.URLDecoder;
//import cn.hutool.core.net.URLEncoder;
//import com.alibaba.fastjson.JSON;
//import com.psbc.cpufp.common.enumcomm.eum.ResponseCodeEnum;
////import com.psbc.cpufp.common.serial.util.SerialNumberGenerateUtils;
//import com.psbc.cpufp.entity.info.FileAttr;
//import com.psbc.cpufp.entity.info.FileInfo;
//import com.psbc.cpufp.entity.info.OutSystemFileNoticeInfo;
//import com.psbc.cpufp.entity.info.ScanParam;
//import com.psbc.cpufp.entity.info.TaskCallbackInfo;
//import com.psbc.cpufp.entity.model.FileSrvGwAdapterDownloadParam;
//import com.psbc.cpufp.entity.model.GeneralParamDownDelModel;
////import com.psbc.cpufp.entity.model.GeneralParamModel;
//import com.psbc.cpufp.entity.model.NoticeParam;
//import com.psbc.cpufp.entity.po.FileStreamPo;
//import com.psbc.cpufp.entity.response.GeneralResponse;
//import com.psbc.cpufp.feign.FileServiceSchedulerFeign;
//import com.psbc.cpufp.feign.FileSrvGwAdapterFeign;
//import feign.Request;
//import feign.Response;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.io.IOUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestPart;
//import org.springframework.web.bind.annotation.RestController;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.io.InputStream;
//import java.io.OutputStream;
//import java.nio.charset.StandardCharsets;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.Collection;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.UUID;
//
///**
// * 文件业务demo
// */
//@Slf4j
//@RestController
//public class FileController {
//
//    private static final Logger log = LoggerFactory.getLogger(FileController.class);
//
//    /**
//     * 字符串.
//     */
//    public static final String POINT_STR = ".";
//
//
//    /**
//     * response code
//     */
//    public static final String RESP_CODE = "respCode";
//
//
//    //响应描述
//    public static final String RESP_DESC = "respDesc";
//
//    @Autowired
//    private FileServiceSchedulerFeign fileServiceSchedulerFeign;
//
//    @Autowired
//    private FileSrvGwAdapterFeign fileSrvGwAdapterFeign;
//
//    @PostMapping(value = "/scan")
//    public List<FileAttr> scan(@RequestBody ScanParam scanParam) {
//        return fileServiceSchedulerFeign.scan(scanParam, "srwer", new Request.Options());
//    }
//
//
//    /**
//     * 文件上传发起文件业务
//     *
//     * @param file file
//     * @return 上传结果
//     */
//    @PostMapping(value = "file/upload")
//    public String uploadFileBuss(@RequestPart MultipartFile file) {
//        OutSystemFileNoticeInfo outSystemFileNoticeInfo = new OutSystemFileNoticeInfo();
//        outSystemFileNoticeInfo.setFileName(file.getOriginalFilename());
//        outSystemFileNoticeInfo.setNewFileBusinessCode("333333");
//        outSystemFileNoticeInfo.setSendSysCode("***********");
//        String time = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS").format(LocalDateTime.now());
//        outSystemFileNoticeInfo.setFileBusiTrackNo(time
//                + "***********" + time.substring(13));
//        Response response = fileServiceSchedulerFeign.uploadFile(file, outSystemFileNoticeInfo, new Request.Options());
//        Collection<String> respCodes = response.headers().get(RESP_CODE);
//        if (respCodes != null && respCodes.contains(ResponseCodeEnum.SUCCESS.getCode())) {
//            return "上传成功";
//        } else {
//            response.headers().get(RESP_DESC).stream().forEach(item -> log.error(URLDecoder.decode(item, StandardCharsets.UTF_8)));
//            return "上传失败";
//        }
//    }
//
//    /**
//     * 文件通知发起文件业务
//     *
//     * @param fileName fileName
//     * @return 通知结果
//     */
//    @PostMapping(value = "file/src/notice")
//    public String taskNotice(@RequestBody String fileName) {
//        OutSystemFileNoticeInfo outSystemFileNoticeInfo = new OutSystemFileNoticeInfo();
//        outSystemFileNoticeInfo.setFileName(fileName);
//        outSystemFileNoticeInfo.setNewFileBusinessCode("444444");
//        outSystemFileNoticeInfo.setSendSysCode("135000100051");
//        String time = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS").format(LocalDateTime.now());
//        outSystemFileNoticeInfo.setFileBusiTrackNo(time
//                + "135000100051" + time.substring(14));
//        Response response = fileServiceSchedulerFeign.taskNotice(outSystemFileNoticeInfo, new Request.Options());
//        Collection<String> respCodes = response.headers().get(RESP_CODE);
//        if (respCodes != null && respCodes.contains(ResponseCodeEnum.SUCCESS.getCode())) {
//            return "通知成功";
//        } else {
//            response.headers().get(RESP_DESC).stream().forEach(item -> log.error(URLDecoder.decode(item, StandardCharsets.UTF_8)));
//            return "通知失败";
//        }
//    }
//
//    /**
//     * 特色业务  异步
//     *
//     * @param fileStreamPo 特色业务参数
//     * @return 特色业务结果
//     */
//    @PostMapping(value = "file/special")
//    public GeneralResponse special(@RequestBody FileStreamPo fileStreamPo) {
//        log.info("异步特色业务：{}", JSON.toJSONString(fileStreamPo));
//        new Thread(new Task(fileServiceSchedulerFeign, fileStreamPo)).start();
//        return GeneralResponse.success();
//    }
//
//    /**
//     * 特色业务  同步
//     *
//     * @param fileStreamPo 特色业务参数
//     * @return 特色业务结果
//     */
//    @PostMapping(value = "file/special/sync")
//    public GeneralResponse<TaskCallbackInfo> syncSpecial(@RequestBody FileStreamPo fileStreamPo) {
//        try {
//            log.info("同步特色业务：{}", JSON.toJSONString(fileStreamPo));
//            Thread.sleep(20000);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
//        return GeneralResponse.success(createTaskCallbackInfo(fileStreamPo));
//    }
//
//    /**
//     * 特色业务  失败
//     *
//     * @param fileStreamPo 特色业务参数
//     * @return 特色业务结果
//     */
//    @PostMapping(value = "file/special/fail")
//    public GeneralResponse<TaskCallbackInfo> fail(@RequestBody FileStreamPo fileStreamPo) {
//        log.info("特色业务失败：{}", JSON.toJSONString(fileStreamPo));
//        return GeneralResponse.failed(ResponseCodeEnum.FAILED, null);
//    }
//
//
//    /**
//     * 特色业务  结果在header
//     *
//     * @param fileStreamPo 特色业务参数
//     */
//    @PostMapping(value = "file/special/fail/header")
//    public void headerFail(HttpServletResponse response, @RequestBody FileStreamPo fileStreamPo) {
//        log.info("特色业务失败Header：{}", JSON.toJSONString(fileStreamPo));
//        URLEncoder urlEncoder = new URLEncoder();
//        response.setHeader("respCode", ResponseCodeEnum.FAILED.getCode());
//        response.setHeader("respDesc", urlEncoder.encode(ResponseCodeEnum.FAILED.getDesc(), StandardCharsets.UTF_8));
//    }
//
//    /**
//     * 通知  同步
//     *
//     * @param noticeParam 特色业务参数
//     * @return 通知结果
//     */
//    @PostMapping(value = "file/notice")
//    public GeneralResponse notice(@RequestBody NoticeParam noticeParam) {
//        try {
//            log.info("文件通知业务：{}", JSON.toJSONString(noticeParam));
//            Thread.sleep(20000);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
//
//        return GeneralResponse.success();
//    }
//
//    /**
//     * 通知  失败
//     *
//     * @param noticeParam 特色业务参数
//     * @return 通知结果
//     */
//    @PostMapping(value = "file/notice/fail")
//    public GeneralResponse<TaskCallbackInfo> noticeFail(@RequestBody NoticeParam noticeParam) {
//        log.info("文件通知业务失败：{}", JSON.toJSONString(noticeParam));
//        return GeneralResponse.failed(ResponseCodeEnum.FAILED, null);
//    }
//
//    /**
//     * 通知  结果在header
//     *
//     * @param noticeParam 特色业务参数
//     */
//    @PostMapping(value = "file/notice/fail/header")
//    public void noticeHeaderFail(HttpServletResponse response, @RequestBody NoticeParam noticeParam) {
//        log.info("文件通知业务失败Header：{}", JSON.toJSONString(noticeParam));
//        URLEncoder urlEncoder = new URLEncoder();
//        response.setHeader("respCode", ResponseCodeEnum.FAILED.getCode());
//        response.setHeader("respDesc", urlEncoder.encode(ResponseCodeEnum.FAILED.getDesc(), StandardCharsets.UTF_8));
//    }
//
//
//    /**
//     * 测试结果
//     *
//     * @return 结果结果
//     */
//    @GetMapping(value = "test")
//    public String test() {
//        return "test:" + UUID.randomUUID().toString();
//    }
//
//    private TaskCallbackInfo createTaskCallbackInfo(FileStreamPo fileStreamPo) {
//        TaskCallbackInfo callbackInfo = new TaskCallbackInfo();
//        callbackInfo.setExtendInfo(new HashMap<>());
//        callbackInfo.setSuccess(true);
//        callbackInfo.setMsg("");
//        List<FileInfo> fileInfos = fileStreamPo.getFileInfos();
//        Map<String, String> fastDfsIdToName = new HashMap<>();
//        Map<String, Object> fileExtendInfo = new HashMap<>();
//        fileInfos.forEach(item -> {
//            fastDfsIdToName.put(item.getFastDfsId(), item.getFileName());
//            fileExtendInfo.put(item.getFastDfsId(), item.getExtendInfo());
//        });
//        callbackInfo.setFastDfsIdToName(fastDfsIdToName);
//        callbackInfo.setFileExtendInfo(fileExtendInfo);
//        callbackInfo.setSubTaskSriNo(fileStreamPo.getSubTaskSriNo());
//        return callbackInfo;
//    }
//
//
//    private static class Task implements Runnable {
//        private FileServiceSchedulerFeign commmidFeignTemp;
//        private FileStreamPo fileStreamPo;
//
//        public Task(FileServiceSchedulerFeign commmidFeignTemp, FileStreamPo fileStreamPo) {
//            this.commmidFeignTemp = commmidFeignTemp;
//            this.fileStreamPo = fileStreamPo;
//        }
//
//        @Override
//        public void run() {
//            try {
//                Thread.sleep(20000);
//            } catch (InterruptedException e) {
//                throw new RuntimeException(e);
//            }
//            TaskCallbackInfo callbackInfo = createTaskCallbackInfo(fileStreamPo);
//            commmidFeignTemp.callback(callbackInfo, new Request.Options());
//        }
//
//        private TaskCallbackInfo createTaskCallbackInfo(FileStreamPo fileStreamPo) {
//            TaskCallbackInfo callbackInfo = new TaskCallbackInfo();
//            callbackInfo.setExtendInfo(new HashMap<>());
//            callbackInfo.setSuccess(true);
//            callbackInfo.setMsg("");
//            List<FileInfo> fileInfos = fileStreamPo.getFileInfos();
//            Map<String, String> fastDfsIdToName = new HashMap<>();
//            Map<String, Object> fileExtendInfo = new HashMap<>();
//            fileInfos.forEach(item -> {
//                fastDfsIdToName.put(item.getFastDfsId(), item.getFileName());
//                fileExtendInfo.put(item.getFastDfsId(), item.getExtendInfo());
//            });
//            callbackInfo.setFastDfsIdToName(fastDfsIdToName);
//            callbackInfo.setFileExtendInfo(fileExtendInfo);
//            callbackInfo.setSubTaskSriNo(fileStreamPo.getSubTaskSriNo());
//            return callbackInfo;
//        }
//    }
//
//    /**
//     * 上传文件  fastDFS
//     *
//     * @param file 文件
//     * @return 上传文件的路径
//     */
//    @RequestMapping("/uploadFile")
//    public String uploadFile(@RequestPart MultipartFile file) {
//        String fileName = file.getOriginalFilename();
//        String suffix = "";
//        if (StringUtils.isEmpty(fileName)) {
//            fileName = "";
//        }
//
//        if (fileName.lastIndexOf(POINT_STR) > 0) {
//            suffix = fileName.substring(fileName.lastIndexOf(POINT_STR) + 1);
//            fileName = fileName.substring(0, fileName.lastIndexOf(POINT_STR));
//        }
//        log.info("{},{}", suffix, fileName);
//
//        //        GeneralParamModel fileUpLoadfo = new GeneralParamModel();
//        //        fileUpLoadfo.setFileName(fileName);
//        //        fileUpLoadfo.setGlobalBusiTrackNo(SerialNumberGenerateUtils.generate());
//        //        fileUpLoadfo.setSuffix(suffix);
//        //        String fileUploadString = JSON.toJSONString(fileUpLoadfo);
//        //        GeneralResponse<Map> generalResponse = fileServiceSchedulerFeign.storageFileUpload(file, fileUploadString, new Request.Options());
//        //        if (generalResponse != null && ResponseCodeEnum.SUCCESS.getCode().equals(generalResponse.getRespCode())) {
//        //            return (String) generalResponse.getData().get("fileId");
//        //        }
//
//        return "";
//    }
//
//    /**
//     * 下载文件 fastDFS
//     *
//     * @param fileId 文件Id
//     */
//    @PostMapping(value = "/file-download")
//    public void fileDownload(HttpServletResponse response, @RequestBody String fileId) {
//        GeneralParamDownDelModel downDelModel = new GeneralParamDownDelModel();
//        //        downDelModel.setFileId(fileId);
//        //        downDelModel.setGlobalBusiTrackNo("20240830170709997119432011000003");
//        Response response1 = fileServiceSchedulerFeign.download(downDelModel, new Request.Options());
//        OutputStream outputStream = null;
//        InputStream inputStream = null;
//        URLEncoder urlEncoder = new URLEncoder();
//        try {
//            response.setHeader(RESP_CODE, ResponseCodeEnum.SUCCESS.getCode());
//            response.setHeader(RESP_DESC, urlEncoder.encode(ResponseCodeEnum.SUCCESS.getDesc(), StandardCharsets.UTF_8));
//            inputStream = response1.body().asInputStream();
//            outputStream = response.getOutputStream();
//            IOUtils.copy(inputStream, outputStream);
//            outputStream.flush();
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            response.setHeader(RESP_CODE, ResponseCodeEnum.FAIL_DOWNLOAD.getCode());
//            response.setHeader(RESP_DESC, urlEncoder.encode(ResponseCodeEnum.FAIL_DOWNLOAD.getDesc(), StandardCharsets.UTF_8));
//        } finally {
//            try {
//                if (inputStream != null) {
//                    inputStream.close();
//                }
//
//                if (outputStream != null) {
//                    outputStream.close();
//                }
//            } catch (IOException e) {
//                log.error(e.getMessage(), e);
//            }
//        }
//    }
//
//    /**
//     * 下载文件  fileSrvGw
//     *
//     * @param fileName 文件名称
//     */
//    @PostMapping(value = "/file-gw-download")
//    public void fileSrvGwDownload(HttpServletResponse response, @RequestBody String fileName) {
//        FileSrvGwAdapterDownloadParam downDelModel = new FileSrvGwAdapterDownloadParam();
//        downDelModel.setFileName(fileName);
//        Response response1 = fileSrvGwAdapterFeign.download(downDelModel, new Request.Options());
//        OutputStream outputStream = null;
//        InputStream inputStream = null;
//        URLEncoder urlEncoder = new URLEncoder();
//        try {
//            response.setHeader(RESP_CODE, ResponseCodeEnum.SUCCESS.getCode());
//            response.setHeader(RESP_DESC, urlEncoder.encode(ResponseCodeEnum.SUCCESS.getDesc(), StandardCharsets.UTF_8));
//            inputStream = response1.body().asInputStream();
//            outputStream = response.getOutputStream();
//            IOUtils.copy(inputStream, outputStream);
//            outputStream.flush();
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            response.setHeader(RESP_CODE, ResponseCodeEnum.FAIL_DOWNLOAD.getCode());
//            response.setHeader(RESP_DESC, urlEncoder.encode(ResponseCodeEnum.FAIL_DOWNLOAD.getDesc(), StandardCharsets.UTF_8));
//        } finally {
//            try {
//                if (inputStream != null) {
//                    inputStream.close();
//                }
//
//                if (outputStream != null) {
//                    outputStream.close();
//                }
//            } catch (IOException e) {
//                log.error(e.getMessage(), e);
//            }
//        }
//    }
//
//
//}
