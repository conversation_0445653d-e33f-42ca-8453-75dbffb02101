//package com.psbc.cpufp.mapper.mapperone;
//
//import org.apache.ibatis.annotations.Param;
//
//import java.util.Map;
//
///**
// * <AUTHOR>
// */
//public interface GetOneMapper {
//    /**
//     * 查询交易名称
//     *
//     * @param frontTxCode 参数
//     * @return 交易名
//     */
//    String selectTxName(@Param("frontTxCode") String frontTxCode);
//
//    /**
//     * 多表查询
//     *
//     * @param frontTxCode frontTxCode
//     * @param flag        flag
//     * @return 查询结果
//     */
//    Map<String, String> selectUniteMultiple(@Param("frontTxCode") String frontTxCode, @Param("flag") String flag);
//}
