# 注意：若该应用无需从集群之外访问，只在集群之内进行服务间的访问，则无需配置 Gateway，删除即可！！！！！！！
##############  Gateway 配置 start #######################
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: gateway-branch-agent
  namespace: {{ .Values.nameSpace }}
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: {{ .Values.gatewayBranchAgent.servers.port }}
      name: http
      protocol: {{ .Values.gatewayBranchAgent.servers.protocol }}
    hosts:
    - "*"
---
##############  Gateway 配置 end ####################

apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: branch-agent
  namespace: {{ .Values.nameSpace }}
spec:
  gateways:
  - gateway-branch-agent
  hosts:
  - "*"  
  #hosts:
  #- branch-agent
  http:
  {{- with .Values.VirtualServiceBranchAgent }}
  - match:
    - uri:
        prefix: {{ .uri }}
    route:
    - destination:
        host: branch-agent
    timeout: {{ .timeout }} # 默认超时时间
    retries:
      attempts:  {{ .retries.attempts }} # 默认两次
  {{- end }}
---

apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: branch-agent
  namespace: {{ .Values.nameSpace }}
spec:
  host: branch-agent
  {{- with .Values.DestinationRuleBranchAgent }}
  trafficPolicy:
    loadBalancer:
      simple: {{ .loadBalancer.simple }}
    connectionPool:
      tcp:
        maxConnections: {{ .connectionPool.tcp.maxConnections }}
        connectTimeout: {{ .connectionPool.tcp.connectTimeout }}
      http:
        http1MaxPendingRequests: {{ .connectionPool.http.http1MaxPendingRequests }}
        maxRequestsPerConnection: {{ .connectionPool.http.maxRequestsPerConnection }}
    outlierDetection:
      consecutive5xxErrors: {{ .outlierDetection.consecutive5xxErrors }}
      interval:  {{ .outlierDetection.interval }}
      baseEjectionTime: {{ .outlierDetection.baseEjectionTime }}
      maxEjectionPercent: {{ .outlierDetection.maxEjectionPercent }}
  {{- end }}
---

apiVersion: microservice.slime.io/v1alpha2
kind: SmartLimiter
metadata:
  name: branch-agent
  namespace: {{ .Values.nameSpace }}
spec:
  sets:
    _base:
      descriptor:
      - action:
          fill_interval:
            seconds: {{ .Values.SmartLimiterBranchAgent.seconds }}
          quota: {{ .Values.SmartLimiterBranchAgent.quota | quote }}
          strategy: "single"
        condition: "true"
        target:
          port: {{ .Values.portResources.branchAgent }}
        match:
        - name: :path
          prefix_match: {{ .Values.SmartLimiterBranchAgent.matchUri }}

