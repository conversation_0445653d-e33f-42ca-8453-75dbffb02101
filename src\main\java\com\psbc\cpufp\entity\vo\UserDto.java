package com.psbc.cpufp.entity.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Data
public class UserDto implements Serializable {
    private Integer userId;
    private String userName;
    private String password;
    private Integer sex;

    public UserDto(int userId,String userName, String pass123, int i) {
        this.userId = userId;
        this.password = pass123;
        this.userName = userName;
        this.sex = i;
    }
    // 省略 getter/setter
}
