//package com.psbc.cpufp.service.impl;
//
//import com.psbc.cpufp.mapper.mappertwo.GetTwoMapper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
///**
// * 数据源2
// *
// * <AUTHOR>
// */
//@Service
//public class GetTwoServiceImpl {
//    /**
//     * 数据源2
//     */
//    @Autowired
//    private GetTwoMapper getTwoMapper;
//
//    public String queryStr(String txCode) {
//        return getTwoMapper.selectTwo(txCode);
//    }
//}
