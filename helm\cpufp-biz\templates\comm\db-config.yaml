apiVersion: v1
kind: ConfigMap
metadata:
  name: db-config-biz
  namespace: {{ .Values.nameSpace }}
data:
  spring_datasource_one_type: "com.alibaba.druid.pool.DruidDataSource"
  spring_datasource_one_driver_class_name: "org.postgresql.Driver"
  spring_datasource_one_url: {{ .Values.comm.dbConfig.one_url | quote }}
  spring_datasource_one_username: {{ .Values.comm.dbConfig.one_username | quote }}
  spring_datasource_one_password: {{ .Values.comm.dbConfig.one_password | quote }}
  spring_datasource_two_type: "com.alibaba.druid.pool.DruidDataSource"
  spring_datasource_two_driver-class-name: "org.postgresql.Driver"
  spring_datasource_two_url: {{ .Values.comm.dbConfig.two_url | quote }}
  spring_datasource_two_username: {{ .Values.comm.dbConfig.two_username | quote }}
  spring_datasource_two_password: {{ .Values.comm.dbConfig.two_password | quote }}
