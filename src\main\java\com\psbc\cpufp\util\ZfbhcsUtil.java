//package com.psbc.cpufp.util;
//
//import cn.hutool.json.JSONObject;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR> Ye
// * @version 1.0.0
// * @title ZfbhcsUtil
// * @description 处理止付保护措施数据
// * @create 2025/4/15 16:44
// **/
//
//
//@Component
//@Slf4j
//public class ZfbhcsUtil {
//
//    @Resource
//    private EncryptAndDecryptTools encryptAndDecryptTools;
//
//
//    /**
//     * 止付保护措施
//     *
//     * @param jsonObject 传过来的参数对象
//     */
//    public void handleZfbhcs(JSONObject jsonObject) {
//        // 对数据进行解密
//        JSONObject dataJsonObject = (JSONObject) jsonObject.get("data");
//        BodyModel bodyModel = new BodyModel();
//        bodyModel.setSignatureValue(dataJsonObject.get("SignatureValue").toString());
//        bodyModel.setContent(dataJsonObject.get("Content").toString());
//        // 对加密部分进行解密
//        String resultStr = encryptAndDecryptTools.decryptData(bodyModel);
//        log.info("解密之后的数据：{}", resultStr);
//        // 组装成总行需要的参数
//
//    }
//}
