package com.psbc.cpufp.entity.enu;

import org.apache.commons.lang3.StringUtils;

/**
 * 浏览器类型枚举类
 */
public enum ExploreTypeEum {
    /**
     * 浏览器类型MSIE
     */
    IE("MSIE"),
    /**
     * 浏览器类型Firefox
     */
    FIREFOX("Firefox"),
    /**
     * 其他浏览器类型
     */
    OTHERS("others");

    /**
     * 类型
     */
    private final String type;

    ExploreTypeEum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    /**
     * 根据agent匹配对应的浏览器枚举值
     *
     * @param agent 代表浏览器的字符串
     * @return 对应的浏览器枚举值
     */
    public static ExploreTypeEum matchType(String agent) {
        if (StringUtils.isBlank(agent)) {
            return OTHERS;
        }
        if (agent.contains(IE.getType())) {
            return IE;
        }
        if (agent.contains(FIREFOX.getType())) {
            return FIREFOX;
        }
        return OTHERS;
    }
}
