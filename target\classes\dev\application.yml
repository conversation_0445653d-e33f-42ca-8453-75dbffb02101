server:
  port: 6666
spring:
  application:
    name: branch-agent
  profiles:
    active: dev
  #监控暴露端点
management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health"
#已启动的kernel的grpc注册地址，如127.0.0.1:20081
soma:
  registryUrl: soma-kernel.cpufp-plat:20081
  #本地内嵌服务启动端口，须保证该端口未被其他进程占用
  remotePort: 9944
  #执行器校验令牌
  #  execAccessToken: b79558f7390c45d8bec48aa03fabaaa7
  execAccessToken: ${soma_token}
  #执行器集群id
  #  execGroupId: EIDbfc5af50-005c-11ee-883c-fa163e7876be
  execGroupId: ${soma_id}
  #该配置项须保证与执行器集群的注册方式一致
  isAutoRegistry: 1
  #日志适配器路径(可选，如果不配置日志打印方式默认为System.out.println)
  loggerAdapter: com.psbc.cpufp.soma.logger.SomaLogger
  #执行调度任务的最大线程数(可选)
  #  remoteMaxThreadNum: 3
  #当前执行器的版本 要与创建的执行器版本一致
  execVersion: default
jasypt:
  encryptor:
    password: ${JASYPT_PASSWORD}