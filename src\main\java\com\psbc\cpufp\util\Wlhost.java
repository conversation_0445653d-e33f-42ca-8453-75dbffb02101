package com.psbc.cpufp.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.net.UnknownHostException;

@Slf4j
public class Wlhost {

  private static final int LEN_SIZE = 6;

  public String sendToWl(String ip, int port, String msg) {
    log.info("+ ip + " + port);
    Socket socket = null;
    InputStream is = null;
    OutputStream os = null;
    String rspmsg = null;
    try {
      socket = new Socket(ip, port);
      is = socket.getInputStream();
      os = socket.getOutputStream();
      String reqmsg = String.valueOf(String.format("%06d", new Object[] { Integer.valueOf((msg.getBytes()).length) })) + msg;
      log.info("向外联请求报文"+reqmsg);
      os.write(reqmsg.getBytes());
      byte[] retHead = new byte[6];
      log.info("读职外联系统返回报文>>>");
      int n = is.read(retHead);
      log.info("外联系统返回报文头:"+ new String(retHead));
      if (n < 6) {
        log.error("接收外联系统错误:报文头长度:"+ new String(retHead));
        throw new  Exception("接收外联系统报文错误:报文头错误");
      }
      int len = Integer.parseInt((new String(retHead)).trim());
      byte[] msgbody = new byte[len];
      n = is.read(msgbody);
      if (n < len)
        throw new  Exception("接收外联系统报文错误:读取长度小于报文头标识长度");
      rspmsg = new String(msgbody);
    } catch (UnknownHostException e) {
      e.printStackTrace();
    } catch (IOException e) {
      e.printStackTrace();
    } catch (Exception e) {
      e.printStackTrace();
    } finally {
      try {
        if (is != null)
          is.close();
        if (os != null)
          os.close();
        if (socket != null)
          socket.close();
      } catch (IOException e) {
        e.printStackTrace();
      }
    }
    return rspmsg;
  }
}
