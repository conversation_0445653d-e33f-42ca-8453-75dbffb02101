package com.psbc.cpufp.client;

import com.psbc.cpufp.service.IWebService;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import java.net.URL;

public class WebServiceClient {
    private static final String WSDL_URL = "http://localhost:6666/services/IWebServiceService?wsdl";

    public static void main(String[] args) {
        try {
            // 创建服务
            URL url = new URL(WSDL_URL);
            QName qname = new QName("http://webservice.psbc.com/", "IWebServiceService");
            Service service = Service.create(url, qname);
            
            // 获取接口实例
            IWebService webService = service.getPort(IWebService.class);
            
            // 调用方法
            String bankId = "YOUR_BANK_ID";
            String inParameter = "<xml>your request xml</xml>";
            String result = webService.Execute(bankId, inParameter);
            
            System.out.println("Response: " + result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}