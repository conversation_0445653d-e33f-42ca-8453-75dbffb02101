
<BugCollection sequence='0' release='' analysisTimestamp='*************' version='3.1.10' timestamp='*************'><Project projectName='branch-agent'><Jar>C:\workspace\bank\branch-agent-xm0628\target\classes</Jar><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\bfes-agent-comm\1.0.11.RELEASE\bfes-agent-comm-1.0.11.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.13.5\jackson-dataformat-xml-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\codehaus\woodstox\stax2-api\4.2.1\stax2-api-4.2.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\woodstox\woodstox-core\6.4.0\woodstox-core-6.4.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\net\sf\json-lib\json-lib\2.4\json-lib-2.4-jdk15.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\net\sf\ezmorph\ezmorph\1.0.6\ezmorph-1.0.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-logging\commons-logging\1.2\commons-logging-1.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\xom\xom\1.2.5\xom-1.2.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\github\openfeign\feign-httpclient\11.10\feign-httpclient-11.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\httpcomponents\httpcore\4.4.13\httpcore-4.4.13.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-codec\commons-codec\1.15\commons-codec-1.15.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\tomcat\embed\tomcat-embed-core\9.0.73\tomcat-embed-core-9.0.73.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\tomcat\tomcat-annotations-api\9.0.73\tomcat-annotations-api-9.0.73.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\common-response-enum\1.0.12.RELEASE\common-response-enum-1.0.12.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\cn\hutool\hutool-all\5.7.20\hutool-all-5.7.20.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-validation\2.7.10\spring-boot-starter-validation-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter\2.7.10\spring-boot-starter-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot\2.7.10\spring-boot-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-autoconfigure\2.7.10\spring-boot-autoconfigure-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-logging\2.7.10\spring-boot-starter-logging-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-core\5.3.26\spring-core-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-jcl\5.3.26\spring-jcl-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\tomcat\embed\tomcat-embed-el\9.0.73\tomcat-embed-el-9.0.73.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\cloud\spring-cloud-starter-openfeign\3.1.6\spring-cloud-starter-openfeign-3.1.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\cloud\spring-cloud-starter\3.1.6\spring-cloud-starter-3.1.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\security\spring-security-rsa\1.0.11.RELEASE\spring-security-rsa-1.0.11.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\bouncycastle\bcpkix-jdk15on\1.69\bcpkix-jdk15on-1.69.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\bouncycastle\bcutil-jdk15on\1.69\bcutil-jdk15on-1.69.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\cloud\spring-cloud-openfeign-core\3.1.6\spring-cloud-openfeign-core-3.1.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-aop\2.7.10\spring-boot-starter-aop-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-web\5.3.26\spring-web-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-beans\5.3.26\spring-beans-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\cloud\spring-cloud-commons\3.1.6\spring-cloud-commons-3.1.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\security\spring-security-crypto\5.7.7\spring-security-crypto-5.7.7.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\github\openfeign\feign-core\11.10\feign-core-11.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\github\openfeign\feign-slf4j\11.10\feign-slf4j-11.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-web\2.7.10\spring-boot-starter-web-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-json\2.7.10\spring-boot-starter-json-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-tomcat\2.7.10\spring-boot-starter-tomcat-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.73\tomcat-embed-websocket-9.0.73.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-webmvc\5.3.26\spring-webmvc-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-aop\5.3.26\spring-aop-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-context\5.3.26\spring-context-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-expression\5.3.26\spring-expression-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\retry\spring-retry\1.3.4\spring-retry-1.3.4.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-actuator\2.7.10\spring-boot-starter-actuator-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-actuator-autoconfigure\2.7.10\spring-boot-actuator-autoconfigure-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-actuator\2.7.10\spring-boot-actuator-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\micrometer\micrometer-core\1.9.9\micrometer-core-1.9.9.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\common.log.frame\1.0.2.RELEASE\common.log.frame-1.0.2.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\yaml\snakeyaml\1.31\snakeyaml-1.31.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\commons\commons-lang3\3.11\commons-lang3-3.11.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\alibaba\fastjson\1.2.83\fastjson-1.2.83.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\codehaus\janino\janino\3.1.9\janino-3.1.9.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\codehaus\janino\commons-compiler\3.1.9\commons-compiler-3.1.9.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\postgresql\postgresql\42.2.26\postgresql-42.2.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\alibaba\druid\1.2.8\druid-1.2.8.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\common.jasypt.decrypt\1.0.2.RELEASE\common.jasypt.decrypt-1.0.2.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\bouncycastle\bcprov-jdk15on\1.69\bcprov-jdk15on-1.69.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\cloud\spring-cloud-context\3.1.6\spring-cloud-context-3.1.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\soma\job\soma_client_java\1.0.6.1.RELEASE\soma_client_java-1.0.6.1.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\grpc\grpc-netty\1.50.0\grpc-netty-1.50.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-codec-http2\4.1.90.Final\netty-codec-http2-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-common\4.1.90.Final\netty-common-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-buffer\4.1.90.Final\netty-buffer-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-transport\4.1.90.Final\netty-transport-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-resolver\4.1.90.Final\netty-resolver-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-codec\4.1.90.Final\netty-codec-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-handler\4.1.90.Final\netty-handler-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-codec-http\4.1.90.Final\netty-codec-http-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\guava\guava\31.1-android\guava-31.1-android.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-transport-native-unix-common\4.1.90.Final\netty-transport-native-unix-common-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\grpc\grpc-core\1.50.0\grpc-core-1.50.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\grpc\grpc-protobuf\1.56.0\grpc-protobuf-1.56.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\api\grpc\proto-google-common-protos\2.17.0\proto-google-common-protos-2.17.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\grpc\grpc-protobuf-lite\1.56.0\grpc-protobuf-lite-1.56.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\grpc\grpc-stub\1.56.0\grpc-stub-1.56.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\github\pagehelper\pagehelper-spring-boot-starter\1.4.1\pagehelper-spring-boot-starter-1.4.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.1\mybatis-spring-boot-starter-2.2.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-jdbc\2.7.10\spring-boot-starter-jdbc-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-jdbc\5.3.26\spring-jdbc-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.1\mybatis-spring-boot-autoconfigure-2.2.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\mybatis\mybatis\3.5.9\mybatis-3.5.9.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.4.1\pagehelper-spring-boot-autoconfigure-1.4.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\github\pagehelper\pagehelper\5.3.1\pagehelper-5.3.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\github\jsqlparser\jsqlparser\4.2\jsqlparser-4.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-io\commons-io\2.7\commons-io-2.7.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\common.serial.api\1.0.2.RELEASE\common.serial.api-1.0.2.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\common.cache.client\1.0.7.RELEASE\common.cache.client-1.0.7.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-data-redis\2.7.10\spring-boot-starter-data-redis-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\data\spring-data-redis\2.7.10\spring-data-redis-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\data\spring-data-keyvalue\2.7.10\spring-data-keyvalue-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\data\spring-data-commons\2.7.10\spring-data-commons-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-tx\5.3.26\spring-tx-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-oxm\5.3.26\spring-oxm-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-context-support\5.3.26\spring-context-support-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\projectreactor\reactor-core\3.4.28\reactor-core-3.4.28.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\redis\clients\jedis\3.8.0\jedis-3.8.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\projectlombok\lombok\1.18.18\lombok-1.18.18.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-spring-boot-starter-jaxws\3.5.5\cxf-spring-boot-starter-jaxws-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-spring-boot-autoconfigure\3.5.5\cxf-spring-boot-autoconfigure-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-rt-features-metrics\3.5.5\cxf-rt-features-metrics-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-rt-frontend-jaxws\3.5.5\cxf-rt-frontend-jaxws-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\xml-resolver\xml-resolver\1.2\xml-resolver-1.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\ow2\asm\asm\9.4\asm-9.4.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-core\3.5.5\cxf-core-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\glassfish\jaxb\jaxb-runtime\2.3.8\jaxb-runtime-2.3.8.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\glassfish\jaxb\txw2\2.3.8\txw2-2.3.8.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\ws\xmlschema\xmlschema-core\2.3.0\xmlschema-core-2.3.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-rt-bindings-soap\3.5.5\cxf-rt-bindings-soap-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-rt-wsdl\3.5.5\cxf-rt-wsdl-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\wsdl4j\wsdl4j\1.6.3\wsdl4j-1.6.3.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-rt-databinding-jaxb\3.5.5\cxf-rt-databinding-jaxb-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-rt-bindings-xml\3.5.5\cxf-rt-bindings-xml-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-rt-frontend-simple\3.5.5\cxf-rt-frontend-simple-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-rt-ws-addr\3.5.5\cxf-rt-ws-addr-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-rt-ws-policy\3.5.5\cxf-rt-ws-policy-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\neethi\neethi\3.2.0\neethi-3.2.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\cxf\cxf-rt-transports-http\3.5.5\cxf-rt-transports-http-3.5.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\dom4j\dom4j\2.1.4\dom4j-2.1.4.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp-security-sdk\1.0.6.RELEASE\cpufp-security-sdk-1.0.6.RELEASE.jar</AuxClasspathEntry><SrcDir>C:\workspace\bank\branch-agent-xm0628\src\main\java</SrcDir><SrcDir>C:\workspace\bank\branch-agent-xm0628\target\generated-sources\annotations</SrcDir><WrkDir>C:\workspace\bank\branch-agent-xm0628\target</WrkDir></Project><BugInstance instanceOccurrenceNum='0' instanceHash='6822e6d3ddc35f2846c6f09639fbf29d' cweid='563' rank='17' abbrev='DLS' category='STYLE' priority='2' type='DLS_DEAD_LOCAL_STORE' instanceOccurrenceMax='0'><ShortMessage>Dead store to local variable</ShortMessage><LongMessage>Dead store to headers in com.psbc.cpufp.httpApplication.MyHttpHandler.getRequestHeader(HttpExchange)</LongMessage><Class classname='com.psbc.cpufp.httpApplication.MyHttpHandler' primary='true'><SourceLine classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='26' end='294' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java'><Message>At MyHttpHandler.java:[lines 26-294]</Message></SourceLine><Message>In class com.psbc.cpufp.httpApplication.MyHttpHandler</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' signature='(Lcom/sun/net/httpserver/HttpExchange;)Ljava/lang/String;' name='getRequestHeader' primary='true'><SourceLine endBytecode='6' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='194' end='196' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.httpApplication.MyHttpHandler.getRequestHeader(HttpExchange)</Message></Method><LocalVariable role='LOCAL_VARIABLE_NAMED' pc='5' name='headers' register='2'><Message>Local variable named headers</Message></LocalVariable><SourceLine endBytecode='4' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='194' end='194' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java' startBytecode='4' primary='true'><Message>At MyHttpHandler.java:[line 194]</Message></SourceLine><Property name='edu.umd.cs.findbugs.detect.DeadLocalStoreProperty.LOCAL_NAME' value='headers'></Property><Property name='edu.umd.cs.findbugs.detect.DeadLocalStoreProperty.METHOD_RESULT' value='true'></Property><Property name='edu.umd.cs.findbugs.detect.DeadLocalStoreProperty.NO_LOADS' value='true'></Property></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='3e17e8941cbe04464bb87dc204bc898f' rank='19' abbrev='Dm' category='I18N' priority='1' type='DM_DEFAULT_ENCODING' instanceOccurrenceMax='0'><ShortMessage>Reliance on default encoding</ShortMessage><LongMessage>Found reliance on default encoding in com.psbc.cpufp.httpApplication.MyHttpHandler.sendZhbg(String, String): new java.io.InputStreamReader(InputStream)</LongMessage><Class classname='com.psbc.cpufp.httpApplication.MyHttpHandler' primary='true'><SourceLine classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='26' end='294' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java'><Message>At MyHttpHandler.java:[lines 26-294]</Message></SourceLine><Message>In class com.psbc.cpufp.httpApplication.MyHttpHandler</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' signature='(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;' name='sendZhbg' primary='true'><SourceLine endBytecode='1654' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='87' end='178' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.httpApplication.MyHttpHandler.sendZhbg(String, String)</Message></Method><Method isStatic='false' role='METHOD_CALLED' classname='java.io.InputStreamReader' signature='(Ljava/io/InputStream;)V' name='&lt;init&gt;'><SourceLine endBytecode='108' classname='java.io.InputStreamReader' start='72' end='79' sourcepath='java/io/InputStreamReader.java' sourcefile='InputStreamReader.java' startBytecode='0'></SourceLine><Message>Called method new java.io.InputStreamReader(InputStream)</Message></Method><SourceLine endBytecode='257' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='121' end='121' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java' startBytecode='257' primary='true'><Message>At MyHttpHandler.java:[line 121]</Message></SourceLine><SourceLine role='SOURCE_LINE_ANOTHER_INSTANCE' endBytecode='632' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='160' end='160' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java' startBytecode='632'><Message>Another occurrence at MyHttpHandler.java:[line 160]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='be4c24a1b2feb77f2e56b78bb02245cb' rank='19' abbrev='Dm' category='I18N' priority='1' type='DM_DEFAULT_ENCODING' instanceOccurrenceMax='0'><ShortMessage>Reliance on default encoding</ShortMessage><LongMessage>Found reliance on default encoding in com.psbc.cpufp.httpApplication.MyHttpHandler.sendZhbg(String, String): String.getBytes()</LongMessage><Class classname='com.psbc.cpufp.httpApplication.MyHttpHandler' primary='true'><SourceLine classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='26' end='294' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java'><Message>At MyHttpHandler.java:[lines 26-294]</Message></SourceLine><Message>In class com.psbc.cpufp.httpApplication.MyHttpHandler</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' signature='(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;' name='sendZhbg' primary='true'><SourceLine endBytecode='1654' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='87' end='178' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.httpApplication.MyHttpHandler.sendZhbg(String, String)</Message></Method><Method isStatic='false' role='METHOD_CALLED' classname='java.lang.String' signature='()[B' name='getBytes'><SourceLine endBytecode='37' classname='java.lang.String' start='958' end='958' sourcepath='java/lang/String.java' sourcefile='String.java' startBytecode='0'></SourceLine><Message>Called method String.getBytes()</Message></Method><SourceLine endBytecode='291' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='125' end='125' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java' startBytecode='291' primary='true'><Message>At MyHttpHandler.java:[line 125]</Message></SourceLine><SourceLine role='SOURCE_LINE_ANOTHER_INSTANCE' endBytecode='666' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='164' end='164' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java' startBytecode='666'><Message>Another occurrence at MyHttpHandler.java:[line 164]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='********************************' rank='16' abbrev='OS' category='BAD_PRACTICE' priority='2' type='OS_OPEN_STREAM' instanceOccurrenceMax='0'><ShortMessage>Method may fail to close stream</ShortMessage><LongMessage>com.psbc.cpufp.httpApplication.MyHttpHandler.getRequestParam(HttpExchange) may fail to close stream</LongMessage><Class classname='com.psbc.cpufp.httpApplication.MyHttpHandler' primary='true'><SourceLine classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='26' end='294' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java'><Message>At MyHttpHandler.java:[lines 26-294]</Message></SourceLine><Message>In class com.psbc.cpufp.httpApplication.MyHttpHandler</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' signature='(Lcom/sun/net/httpserver/HttpExchange;)Ljava/lang/String;' name='getRequestParam' primary='true'><SourceLine endBytecode='42' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='218' end='246' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.httpApplication.MyHttpHandler.getRequestParam(HttpExchange)</Message></Method><Type role='TYPE_CLOSEIT' descriptor='Ljava/io/Reader;'><SourceLine classname='java.io.Reader' start='66' end='249' sourcepath='java/io/Reader.java' sourcefile='Reader.java'><Message>At Reader.java:[lines 66-249]</Message></SourceLine><Message>Need to close java.io.Reader </Message></Type><SourceLine endBytecode='26' classname='com.psbc.cpufp.httpApplication.MyHttpHandler' start='230' end='230' sourcepath='com/psbc/cpufp/httpApplication/MyHttpHandler.java' sourcefile='MyHttpHandler.java' startBytecode='26' primary='true'><Message>At MyHttpHandler.java:[line 230]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='903ea304ec82ed42b3368ccd265c7308' rank='16' abbrev='Nm' category='BAD_PRACTICE' priority='2' type='NM_METHOD_NAMING_CONVENTION' instanceOccurrenceMax='0'><ShortMessage>Method names should start with a lower case letter</ShortMessage><LongMessage>The method name com.psbc.cpufp.service.impl.WebServiceImpl.Execute(String, String) doesn't start with a lower case letter</LongMessage><Class classname='com.psbc.cpufp.service.impl.WebServiceImpl' primary='true'><SourceLine classname='com.psbc.cpufp.service.impl.WebServiceImpl' start='15' end='44' sourcepath='com/psbc/cpufp/service/impl/WebServiceImpl.java' sourcefile='WebServiceImpl.java'><Message>At WebServiceImpl.java:[lines 15-44]</Message></SourceLine><Message>In class com.psbc.cpufp.service.impl.WebServiceImpl</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.service.impl.WebServiceImpl' signature='(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;' name='Execute' primary='true'><SourceLine endBytecode='410' classname='com.psbc.cpufp.service.impl.WebServiceImpl' start='30' end='44' sourcepath='com/psbc/cpufp/service/impl/WebServiceImpl.java' sourcefile='WebServiceImpl.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.service.impl.WebServiceImpl.Execute(String, String)</Message></Method><SourceLine synthetic='true' endBytecode='410' classname='com.psbc.cpufp.service.impl.WebServiceImpl' start='30' end='44' sourcepath='com/psbc/cpufp/service/impl/WebServiceImpl.java' sourcefile='WebServiceImpl.java' startBytecode='0'><Message>At WebServiceImpl.java:[lines 30-44]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='7b1237ce7373ebf8ae9af911410ba65e' cweid='476' rank='6' abbrev='NP' category='CORRECTNESS' priority='1' type='NP_NULL_ON_SOME_PATH' instanceOccurrenceMax='0'><ShortMessage>Possible null pointer dereference</ShortMessage><LongMessage>Possible null pointer dereference of port in new com.psbc.cpufp.socket.PayServerSocket()</LongMessage><Class classname='com.psbc.cpufp.socket.PayServerSocket' primary='true'><SourceLine classname='com.psbc.cpufp.socket.PayServerSocket' start='11' end='68' sourcepath='com/psbc/cpufp/socket/PayServerSocket.java' sourcefile='PayServerSocket.java'><Message>At PayServerSocket.java:[lines 11-68]</Message></SourceLine><Message>In class com.psbc.cpufp.socket.PayServerSocket</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.socket.PayServerSocket' signature='()V' name='&lt;init&gt;' primary='true'><SourceLine endBytecode='309' classname='com.psbc.cpufp.socket.PayServerSocket' start='19' end='38' sourcepath='com/psbc/cpufp/socket/PayServerSocket.java' sourcefile='PayServerSocket.java' startBytecode='0'></SourceLine><Message>In method new com.psbc.cpufp.socket.PayServerSocket()</Message></Method><LocalVariable role='LOCAL_VARIABLE_VALUE_OF' pc='68' name='port' register='1'><Message>Value loaded from port</Message></LocalVariable><SourceLine role='SOURCE_LINE_DEREF' endBytecode='69' classname='com.psbc.cpufp.socket.PayServerSocket' start='28' end='28' sourcepath='com/psbc/cpufp/socket/PayServerSocket.java' sourcefile='PayServerSocket.java' startBytecode='69' primary='true'><Message>Dereferenced at PayServerSocket.java:[line 28]</Message></SourceLine><SourceLine role='SOURCE_LINE_NULL_VALUE' endBytecode='20' classname='com.psbc.cpufp.socket.PayServerSocket' start='22' end='22' sourcepath='com/psbc/cpufp/socket/PayServerSocket.java' sourcefile='PayServerSocket.java' startBytecode='20'><Message>Null value at PayServerSocket.java:[line 22]</Message></SourceLine><SourceLine role='SOURCE_LINE_KNOWN_NULL' endBytecode='23' classname='com.psbc.cpufp.socket.PayServerSocket' start='23' end='23' sourcepath='com/psbc/cpufp/socket/PayServerSocket.java' sourcefile='PayServerSocket.java' startBytecode='23'><Message>Known null at PayServerSocket.java:[line 23]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='8bf0839f5848eabb94279e65ac029581' rank='19' abbrev='Dm' category='I18N' priority='1' type='DM_DEFAULT_ENCODING' instanceOccurrenceMax='0'><ShortMessage>Reliance on default encoding</ShortMessage><LongMessage>Found reliance on default encoding in com.psbc.cpufp.socket.ServerThread.run(): new String(byte[])</LongMessage><Class classname='com.psbc.cpufp.socket.ServerThread' primary='true'><SourceLine classname='com.psbc.cpufp.socket.ServerThread' start='32' end='364' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java'><Message>At ServerThread.java:[lines 32-364]</Message></SourceLine><Message>In class com.psbc.cpufp.socket.ServerThread</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.socket.ServerThread' signature='()V' name='run' primary='true'><SourceLine endBytecode='2365' classname='com.psbc.cpufp.socket.ServerThread' start='181' end='341' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.socket.ServerThread.run()</Message></Method><Method isStatic='false' role='METHOD_CALLED' classname='java.lang.String' signature='([B)V' name='&lt;init&gt;'><SourceLine endBytecode='36' classname='java.lang.String' start='566' end='567' sourcepath='java/lang/String.java' sourcefile='String.java' startBytecode='0'></SourceLine><Message>Called method new String(byte[])</Message></Method><SourceLine endBytecode='128' classname='com.psbc.cpufp.socket.ServerThread' start='200' end='200' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java' startBytecode='128' primary='true'><Message>At ServerThread.java:[line 200]</Message></SourceLine><SourceLine role='SOURCE_LINE_ANOTHER_INSTANCE' endBytecode='153' classname='com.psbc.cpufp.socket.ServerThread' start='201' end='201' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java' startBytecode='153'><Message>Another occurrence at ServerThread.java:[line 201]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='8c2b714027cf3423778b919c8772c1c2' rank='19' abbrev='Dm' category='I18N' priority='1' type='DM_DEFAULT_ENCODING' instanceOccurrenceMax='0'><ShortMessage>Reliance on default encoding</ShortMessage><LongMessage>Found reliance on default encoding in com.psbc.cpufp.socket.ServerThread.sendZhbg(String, String): new java.io.InputStreamReader(InputStream)</LongMessage><Class classname='com.psbc.cpufp.socket.ServerThread' primary='true'><SourceLine classname='com.psbc.cpufp.socket.ServerThread' start='32' end='364' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java'><Message>At ServerThread.java:[lines 32-364]</Message></SourceLine><Message>In class com.psbc.cpufp.socket.ServerThread</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.socket.ServerThread' signature='(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;' name='sendZhbg' primary='true'><SourceLine endBytecode='1690' classname='com.psbc.cpufp.socket.ServerThread' start='81' end='175' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.socket.ServerThread.sendZhbg(String, String)</Message></Method><Method isStatic='false' role='METHOD_CALLED' classname='java.io.InputStreamReader' signature='(Ljava/io/InputStream;)V' name='&lt;init&gt;'><SourceLine endBytecode='108' classname='java.io.InputStreamReader' start='72' end='79' sourcepath='java/io/InputStreamReader.java' sourcefile='InputStreamReader.java' startBytecode='0'></SourceLine><Message>Called method new java.io.InputStreamReader(InputStream)</Message></Method><SourceLine endBytecode='257' classname='com.psbc.cpufp.socket.ServerThread' start='115' end='115' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java' startBytecode='257' primary='true'><Message>At ServerThread.java:[line 115]</Message></SourceLine><SourceLine role='SOURCE_LINE_ANOTHER_INSTANCE' endBytecode='629' classname='com.psbc.cpufp.socket.ServerThread' start='155' end='155' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java' startBytecode='629'><Message>Another occurrence at ServerThread.java:[line 155]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='91c63a7da08818546ec0e548552eddff' rank='19' abbrev='Dm' category='I18N' priority='1' type='DM_DEFAULT_ENCODING' instanceOccurrenceMax='0'><ShortMessage>Reliance on default encoding</ShortMessage><LongMessage>Found reliance on default encoding in com.psbc.cpufp.socket.ServerThread.sendZhbg(String, String): String.getBytes()</LongMessage><Class classname='com.psbc.cpufp.socket.ServerThread' primary='true'><SourceLine classname='com.psbc.cpufp.socket.ServerThread' start='32' end='364' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java'><Message>At ServerThread.java:[lines 32-364]</Message></SourceLine><Message>In class com.psbc.cpufp.socket.ServerThread</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.socket.ServerThread' signature='(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;' name='sendZhbg' primary='true'><SourceLine endBytecode='1690' classname='com.psbc.cpufp.socket.ServerThread' start='81' end='175' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.socket.ServerThread.sendZhbg(String, String)</Message></Method><Method isStatic='false' role='METHOD_CALLED' classname='java.lang.String' signature='()[B' name='getBytes'><SourceLine endBytecode='37' classname='java.lang.String' start='958' end='958' sourcepath='java/lang/String.java' sourcefile='String.java' startBytecode='0'></SourceLine><Message>Called method String.getBytes()</Message></Method><SourceLine endBytecode='291' classname='com.psbc.cpufp.socket.ServerThread' start='119' end='119' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java' startBytecode='291' primary='true'><Message>At ServerThread.java:[line 119]</Message></SourceLine><SourceLine role='SOURCE_LINE_ANOTHER_INSTANCE' endBytecode='663' classname='com.psbc.cpufp.socket.ServerThread' start='159' end='159' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java' startBytecode='663'><Message>Another occurrence at ServerThread.java:[line 159]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='f82373d1a11a0900ba76be7aac990241' rank='16' abbrev='RR' category='BAD_PRACTICE' priority='2' type='RR_NOT_CHECKED' instanceOccurrenceMax='0'><ShortMessage>Method ignores results of InputStream.read()</ShortMessage><LongMessage>com.psbc.cpufp.socket.ServerThread.run() ignores result of java.io.DataInputStream.read(byte[])</LongMessage><Class classname='com.psbc.cpufp.socket.ServerThread' primary='true'><SourceLine classname='com.psbc.cpufp.socket.ServerThread' start='32' end='364' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java'><Message>At ServerThread.java:[lines 32-364]</Message></SourceLine><Message>In class com.psbc.cpufp.socket.ServerThread</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.socket.ServerThread' signature='()V' name='run' primary='true'><SourceLine endBytecode='2365' classname='com.psbc.cpufp.socket.ServerThread' start='181' end='341' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.socket.ServerThread.run()</Message></Method><Method isStatic='false' role='METHOD_CALLED' classname='java.io.DataInputStream' signature='([B)I' name='read'><SourceLine endBytecode='35' classname='java.io.DataInputStream' start='100' end='100' sourcepath='java/io/DataInputStream.java' sourcefile='DataInputStream.java' startBytecode='0'></SourceLine><Message>Called method java.io.DataInputStream.read(byte[])</Message></Method><SourceLine endBytecode='103' classname='com.psbc.cpufp.socket.ServerThread' start='199' end='199' sourcepath='com/psbc/cpufp/socket/ServerThread.java' sourcefile='ServerThread.java' startBytecode='103' primary='true'><Message>At ServerThread.java:[line 199]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='63071a5da12880e1d1ef1c17ee6a6575' rank='19' abbrev='Dm' category='I18N' priority='1' type='DM_DEFAULT_ENCODING' instanceOccurrenceMax='0'><ShortMessage>Reliance on default encoding</ShortMessage><LongMessage>Found reliance on default encoding in com.psbc.cpufp.socket.SocketListener$MyHandler.handle(HttpExchange): String.getBytes()</LongMessage><Class classname='com.psbc.cpufp.socket.SocketListener$MyHandler' primary='true'><SourceLine classname='com.psbc.cpufp.socket.SocketListener$MyHandler' start='61' end='76' sourcepath='com/psbc/cpufp/socket/SocketListener.java' sourcefile='SocketListener.java'><Message>At SocketListener.java:[lines 61-76]</Message></SourceLine><Message>In class com.psbc.cpufp.socket.SocketListener$MyHandler</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.socket.SocketListener$MyHandler' signature='(Lcom/sun/net/httpserver/HttpExchange;)V' name='handle' primary='true'><SourceLine endBytecode='177' classname='com.psbc.cpufp.socket.SocketListener$MyHandler' start='67' end='76' sourcepath='com/psbc/cpufp/socket/SocketListener.java' sourcefile='SocketListener.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.socket.SocketListener$MyHandler.handle(HttpExchange)</Message></Method><Method isStatic='false' role='METHOD_CALLED' classname='java.lang.String' signature='()[B' name='getBytes'><SourceLine endBytecode='37' classname='java.lang.String' start='958' end='958' sourcepath='java/lang/String.java' sourcefile='String.java' startBytecode='0'></SourceLine><Message>Called method String.getBytes()</Message></Method><SourceLine endBytecode='56' classname='com.psbc.cpufp.socket.SocketListener$MyHandler' start='74' end='74' sourcepath='com/psbc/cpufp/socket/SocketListener.java' sourcefile='SocketListener.java' startBytecode='56' primary='true'><Message>At SocketListener.java:[line 74]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='854f55365910a552a5cdae61041f9caa' rank='19' abbrev='Dm' category='I18N' priority='1' type='DM_DEFAULT_ENCODING' instanceOccurrenceMax='0'><ShortMessage>Reliance on default encoding</ShortMessage><LongMessage>Found reliance on default encoding in com.psbc.cpufp.socket.SocketListener$MyHandler2.handle(HttpExchange): String.getBytes()</LongMessage><Class classname='com.psbc.cpufp.socket.SocketListener$MyHandler2' primary='true'><SourceLine classname='com.psbc.cpufp.socket.SocketListener$MyHandler2' start='79' end='92' sourcepath='com/psbc/cpufp/socket/SocketListener.java' sourcefile='SocketListener.java'><Message>At SocketListener.java:[lines 79-92]</Message></SourceLine><Message>In class com.psbc.cpufp.socket.SocketListener$MyHandler2</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.socket.SocketListener$MyHandler2' signature='(Lcom/sun/net/httpserver/HttpExchange;)V' name='handle' primary='true'><SourceLine endBytecode='198' classname='com.psbc.cpufp.socket.SocketListener$MyHandler2' start='85' end='92' sourcepath='com/psbc/cpufp/socket/SocketListener.java' sourcefile='SocketListener.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.socket.SocketListener$MyHandler2.handle(HttpExchange)</Message></Method><Method isStatic='false' role='METHOD_CALLED' classname='java.lang.String' signature='()[B' name='getBytes'><SourceLine endBytecode='37' classname='java.lang.String' start='958' end='958' sourcepath='java/lang/String.java' sourcefile='String.java' startBytecode='0'></SourceLine><Message>Called method String.getBytes()</Message></Method><SourceLine endBytecode='77' classname='com.psbc.cpufp.socket.SocketListener$MyHandler2' start='90' end='90' sourcepath='com/psbc/cpufp/socket/SocketListener.java' sourcefile='SocketListener.java' startBytecode='77' primary='true'><Message>At SocketListener.java:[line 90]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='8f0e41e9fb8ff2d585ec4bda28013623' rank='19' abbrev='Dm' category='I18N' priority='1' type='DM_DEFAULT_ENCODING' instanceOccurrenceMax='0'><ShortMessage>Reliance on default encoding</ShortMessage><LongMessage>Found reliance on default encoding in com.psbc.cpufp.util.Wlhost.sendToWl(String, int, String): new String(byte[])</LongMessage><Class classname='com.psbc.cpufp.util.Wlhost' primary='true'><SourceLine classname='com.psbc.cpufp.util.Wlhost' start='11' end='61' sourcepath='com/psbc/cpufp/util/Wlhost.java' sourcefile='Wlhost.java'><Message>At Wlhost.java:[lines 11-61]</Message></SourceLine><Message>In class com.psbc.cpufp.util.Wlhost</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.util.Wlhost' signature='(Ljava/lang/String;ILjava/lang/String;)Ljava/lang/String;' name='sendToWl' primary='true'><SourceLine endBytecode='1422' classname='com.psbc.cpufp.util.Wlhost' start='17' end='61' sourcepath='com/psbc/cpufp/util/Wlhost.java' sourcefile='Wlhost.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.util.Wlhost.sendToWl(String, int, String)</Message></Method><Method isStatic='false' role='METHOD_CALLED' classname='java.lang.String' signature='([B)V' name='&lt;init&gt;'><SourceLine endBytecode='36' classname='java.lang.String' start='566' end='567' sourcepath='java/lang/String.java' sourcefile='String.java' startBytecode='0'></SourceLine><Message>Called method new String(byte[])</Message></Method><SourceLine endBytecode='190' classname='com.psbc.cpufp.util.Wlhost' start='32' end='32' sourcepath='com/psbc/cpufp/util/Wlhost.java' sourcefile='Wlhost.java' startBytecode='190' primary='true'><Message>At Wlhost.java:[line 32]</Message></SourceLine><SourceLine role='SOURCE_LINE_ANOTHER_INSTANCE' endBytecode='232' classname='com.psbc.cpufp.util.Wlhost' start='34' end='34' sourcepath='com/psbc/cpufp/util/Wlhost.java' sourcefile='Wlhost.java' startBytecode='232'><Message>Another occurrence at Wlhost.java:[line 34]</Message></SourceLine><SourceLine role='SOURCE_LINE_ANOTHER_INSTANCE' endBytecode='262' classname='com.psbc.cpufp.util.Wlhost' start='37' end='37' sourcepath='com/psbc/cpufp/util/Wlhost.java' sourcefile='Wlhost.java' startBytecode='262'><Message>Another occurrence at Wlhost.java:[line 37]</Message></SourceLine><SourceLine role='SOURCE_LINE_ANOTHER_INSTANCE' endBytecode='311' classname='com.psbc.cpufp.util.Wlhost' start='42' end='42' sourcepath='com/psbc/cpufp/util/Wlhost.java' sourcefile='Wlhost.java' startBytecode='311'><Message>Another occurrence at Wlhost.java:[line 42]</Message></SourceLine></BugInstance><BugInstance instanceOccurrenceNum='0' instanceHash='d94684085d9f6284d3b9e16dc22c741f' rank='19' abbrev='Dm' category='I18N' priority='1' type='DM_DEFAULT_ENCODING' instanceOccurrenceMax='0'><ShortMessage>Reliance on default encoding</ShortMessage><LongMessage>Found reliance on default encoding in com.psbc.cpufp.util.Wlhost.sendToWl(String, int, String): String.getBytes()</LongMessage><Class classname='com.psbc.cpufp.util.Wlhost' primary='true'><SourceLine classname='com.psbc.cpufp.util.Wlhost' start='11' end='61' sourcepath='com/psbc/cpufp/util/Wlhost.java' sourcefile='Wlhost.java'><Message>At Wlhost.java:[lines 11-61]</Message></SourceLine><Message>In class com.psbc.cpufp.util.Wlhost</Message></Class><Method isStatic='false' classname='com.psbc.cpufp.util.Wlhost' signature='(Ljava/lang/String;ILjava/lang/String;)Ljava/lang/String;' name='sendToWl' primary='true'><SourceLine endBytecode='1422' classname='com.psbc.cpufp.util.Wlhost' start='17' end='61' sourcepath='com/psbc/cpufp/util/Wlhost.java' sourcefile='Wlhost.java' startBytecode='0'></SourceLine><Message>In method com.psbc.cpufp.util.Wlhost.sendToWl(String, int, String)</Message></Method><Method isStatic='false' role='METHOD_CALLED' classname='java.lang.String' signature='()[B' name='getBytes'><SourceLine endBytecode='37' classname='java.lang.String' start='958' end='958' sourcepath='java/lang/String.java' sourcefile='String.java' startBytecode='0'></SourceLine><Message>Called method String.getBytes()</Message></Method><SourceLine endBytecode='80' classname='com.psbc.cpufp.util.Wlhost' start='26' end='26' sourcepath='com/psbc/cpufp/util/Wlhost.java' sourcefile='Wlhost.java' startBytecode='80' primary='true'><Message>At Wlhost.java:[line 26]</Message></SourceLine><SourceLine role='SOURCE_LINE_ANOTHER_INSTANCE' endBytecode='138' classname='com.psbc.cpufp.util.Wlhost' start='28' end='28' sourcepath='com/psbc/cpufp/util/Wlhost.java' sourcefile='Wlhost.java' startBytecode='138'><Message>Another occurrence at Wlhost.java:[line 28]</Message></SourceLine></BugInstance><BugCategory category='BAD_PRACTICE'><Description>Bad practice</Description></BugCategory><BugCategory category='CORRECTNESS'><Description>Correctness</Description></BugCategory><BugCategory category='STYLE'><Description>Dodgy code</Description></BugCategory><BugCategory category='I18N'><Description>Internationalization</Description></BugCategory><BugPattern abbrev='DLS' category='STYLE' type='DLS_DEAD_LOCAL_STORE'><ShortDescription>Dead store to local variable</ShortDescription><Details>

&lt;p&gt;
This instruction assigns a value to a local variable,
but the value is not read or used in any subsequent instruction.
Often, this indicates an error, because the value computed is never
used.
&lt;/p&gt;
&lt;p&gt;
Note that Sun's javac compiler often generates dead stores for
final local variables.  Because SpotBugs is a bytecode-based tool,
there is no easy way to eliminate these false positives.
&lt;/p&gt;

    </Details></BugPattern><BugPattern abbrev='NP' category='CORRECTNESS' type='NP_NULL_ON_SOME_PATH'><ShortDescription>Possible null pointer dereference</ShortDescription><Details>

&lt;p&gt; There is a branch of statement that, &lt;em&gt;if executed,&lt;/em&gt;  guarantees that
a null value will be dereferenced, which
would generate a &lt;code&gt;NullPointerException&lt;/code&gt; when the code is executed.
Of course, the problem might be that the branch or statement is infeasible and that
the null pointer exception can't ever be executed; deciding that is beyond the ability of SpotBugs.
&lt;/p&gt;

    </Details></BugPattern><BugPattern abbrev='Nm' category='BAD_PRACTICE' type='NM_METHOD_NAMING_CONVENTION'><ShortDescription>Method names should start with a lower case letter</ShortDescription><Details>

  &lt;p&gt;
Methods should be verbs, in mixed case with the first letter lowercase, with the first letter of each internal word capitalized.
&lt;/p&gt;

    </Details></BugPattern><BugPattern abbrev='RR' category='BAD_PRACTICE' type='RR_NOT_CHECKED'><ShortDescription>Method ignores results of InputStream.read()</ShortDescription><Details>

  &lt;p&gt; This method ignores the return value of one of the variants of
  &lt;code&gt;java.io.InputStream.read()&lt;/code&gt; which can return multiple bytes.&amp;nbsp;
  If the return value is not checked, the caller will not be able to correctly
  handle the case where fewer bytes were read than the caller requested.&amp;nbsp;
  This is a particularly insidious kind of bug, because in many programs,
  reads from input streams usually do read the full amount of data requested,
  causing the program to fail only sporadically.&lt;/p&gt;

    </Details></BugPattern><BugPattern abbrev='Dm' category='I18N' type='DM_DEFAULT_ENCODING'><ShortDescription>Reliance on default encoding</ShortDescription><Details>

&lt;p&gt; Found a call to a method which will perform a byte to String (or String to byte) conversion, and will assume that the default platform encoding is suitable. This will cause the application behaviour to vary between platforms. Use an alternative API and specify a charset name or Charset object explicitly.  &lt;/p&gt;

      </Details></BugPattern><BugPattern abbrev='OS' category='BAD_PRACTICE' type='OS_OPEN_STREAM'><ShortDescription>Method may fail to close stream</ShortDescription><Details>

&lt;p&gt; The method creates an IO stream object, does not assign it to any
fields, pass it to other methods that might close it,
or return it, and does not appear to close
the stream on all paths out of the method.&amp;nbsp; This may result in
a file descriptor leak.&amp;nbsp; It is generally a good
idea to use a &lt;code&gt;finally&lt;/code&gt; block to ensure that streams are
closed.&lt;/p&gt;

    </Details></BugPattern><BugCode abbrev='RR'><Description>Method ignores results of InputStream.read()</Description></BugCode><BugCode cweid='476' abbrev='NP'><Description>Null pointer dereference</Description></BugCode><BugCode abbrev='OS'><Description>Stream not closed on all paths</Description></BugCode><BugCode abbrev='Dm'><Description>Dubious method used</Description></BugCode><BugCode cweid='563' abbrev='DLS'><Description>Dead local store</Description></BugCode><BugCode abbrev='Nm'><Description>Confusing method name</Description></BugCode><Errors missingClasses='0' errors='1'><Error><ErrorMessage>Unable to read filter: C:\workspace\bank\branch-agent-xm0628\target\sourceFiles.xml : 文件提前结束。</ErrorMessage><Exception>java.io.IOException: 文件提前结束。</Exception><StackTrace>edu.umd.cs.findbugs.filter.Filter.&lt;init&gt;(Filter.java:134)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs.configureFilter(FindBugs.java:512)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs2.addFilter(FindBugs2.java:372)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs2.configureFilters(FindBugs2.java:500)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs2.setUserPreferences(FindBugs2.java:473)</StackTrace><StackTrace>edu.umd.cs.findbugs.TextUICommandLine.configureEngine(TextUICommandLine.java:672)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs.processCommandLine(FindBugs.java:365)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs2.main(FindBugs2.java:1175)</StackTrace></Error></Errors><FindBugsSummary num_packages='16' total_classes='35' priority_1='10' priority_2='4' total_size='1066' clock_seconds='8.13' referenced_classes='242' vm_version='25.391-b13' total_bugs='14' java_version='1.8.0_391' gc_seconds='0.13' alloc_mbytes='455.50' cpu_seconds='33.56' peak_mbytes='229.71' timestamp='Sat, 28 Jun 2025 03:02:11 +0800'><FileStats path='com/psbc/cpufp/BranchAgentApplication.java' size='6' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/config/FpSmConfig.java' size='28' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/config/XmysfzjjgProperties.java' size='27' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/config/ZhbgProperties.java' size='35' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/constant/KeyModelConstant.java' size='6' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/controller/FpController.java' size='38' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/indto/BankInfoCheckDto.java' size='23' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/indto/DgAccountQueryDto.java' size='19' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/indto/HeadDto.java' size='31' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/indto/RootXmlDto.java' size='14' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/outdto/ResponseBankInfoCheckDto.java' size='39' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/outdto/ResponseDgAccountQueryDto.java' size='19' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/outdto/ResponseHeadDto.java' size='39' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/outdto/ResponseRootXmlDto.java' size='14' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/enu/ExploreTypeEum.java' size='27' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/enu/responsecodeenum/GlobalReponseCodeEnum.java' size='25' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/vo/XzpDataVo.java' size='29' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/exception/GlobalException.java' size='4' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/exception/handler/GlobalExceptionHandler.java' size='18' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/httpApplication/MyHttpHandler.java' size='136' bugHash='2330af95bb049d90d91e0f1eaaae2cb7' bugCount='4'></FileStats><FileStats path='com/psbc/cpufp/service/ClientIWebService.java' size='2' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/service/IWebService.java' size='3' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/service/impl/WebServiceImpl.java' size='25' bugHash='d722cff55d3197d4ed8cfc21cb1c04d4' bugCount='1'></FileStats><FileStats path='com/psbc/cpufp/socket/PayServerSocket.java' size='46' bugHash='9aa717743833d8b65e09892ce6c0f882' bugCount='1'></FileStats><FileStats path='com/psbc/cpufp/socket/ServerThread.java' size='216' bugHash='6bbf9effd925291f8b8357a578907eeb' bugCount='4'></FileStats><FileStats path='com/psbc/cpufp/socket/SocketListener.java' size='56' bugHash='b40117e524b3eb6d4b627988e63980e5' bugCount='2'></FileStats><FileStats path='com/psbc/cpufp/util/MacUtil.java' size='7' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/util/SM2KeyPairUtil.java' size='65' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/util/Wlhost.java' size='50' bugHash='0205a9de5c4a40a7f3c72fa4ecd5119e' bugCount='2'></FileStats><FileStats path='com/psbc/cpufp/util/XmlUtils.java' size='19' bugCount='0'></FileStats><PackageStats package='com.psbc.cpufp' total_bugs='0' total_size='6' total_types='1'><ClassStats bugs='0' size='6' interface='false' sourceFile='BranchAgentApplication.java' class='com.psbc.cpufp.BranchAgentApplication'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.config' total_bugs='0' total_size='90' total_types='3'><ClassStats bugs='0' size='28' interface='false' sourceFile='FpSmConfig.java' class='com.psbc.cpufp.config.FpSmConfig'></ClassStats><ClassStats bugs='0' size='27' interface='false' sourceFile='XmysfzjjgProperties.java' class='com.psbc.cpufp.config.XmysfzjjgProperties'></ClassStats><ClassStats bugs='0' size='35' interface='false' sourceFile='ZhbgProperties.java' class='com.psbc.cpufp.config.ZhbgProperties'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.constant' total_bugs='0' total_size='6' total_types='1'><ClassStats bugs='0' size='6' interface='false' sourceFile='KeyModelConstant.java' class='com.psbc.cpufp.constant.KeyModelConstant'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.controller' total_bugs='0' total_size='38' total_types='3'><ClassStats bugs='0' size='30' interface='false' sourceFile='FpController.java' class='com.psbc.cpufp.controller.FpController'></ClassStats><ClassStats bugs='0' size='4' interface='false' sourceFile='FpController.java' class='com.psbc.cpufp.controller.FpController$1'></ClassStats><ClassStats bugs='0' size='4' interface='false' sourceFile='FpController.java' class='com.psbc.cpufp.controller.FpController$2'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.entity.dto.indto' total_bugs='0' total_size='87' total_types='4'><ClassStats bugs='0' size='23' interface='false' sourceFile='BankInfoCheckDto.java' class='com.psbc.cpufp.entity.dto.indto.BankInfoCheckDto'></ClassStats><ClassStats bugs='0' size='19' interface='false' sourceFile='DgAccountQueryDto.java' class='com.psbc.cpufp.entity.dto.indto.DgAccountQueryDto'></ClassStats><ClassStats bugs='0' size='31' interface='false' sourceFile='HeadDto.java' class='com.psbc.cpufp.entity.dto.indto.HeadDto'></ClassStats><ClassStats bugs='0' size='14' interface='false' sourceFile='RootXmlDto.java' class='com.psbc.cpufp.entity.dto.indto.RootXmlDto'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.entity.dto.outdto' total_bugs='0' total_size='111' total_types='4'><ClassStats bugs='0' size='39' interface='false' sourceFile='ResponseBankInfoCheckDto.java' class='com.psbc.cpufp.entity.dto.outdto.ResponseBankInfoCheckDto'></ClassStats><ClassStats bugs='0' size='19' interface='false' sourceFile='ResponseDgAccountQueryDto.java' class='com.psbc.cpufp.entity.dto.outdto.ResponseDgAccountQueryDto'></ClassStats><ClassStats bugs='0' size='39' interface='false' sourceFile='ResponseHeadDto.java' class='com.psbc.cpufp.entity.dto.outdto.ResponseHeadDto'></ClassStats><ClassStats bugs='0' size='14' interface='false' sourceFile='ResponseRootXmlDto.java' class='com.psbc.cpufp.entity.dto.outdto.ResponseRootXmlDto'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.entity.enu' total_bugs='0' total_size='27' total_types='1'><ClassStats bugs='0' size='27' interface='false' sourceFile='ExploreTypeEum.java' class='com.psbc.cpufp.entity.enu.ExploreTypeEum'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.entity.enu.responsecodeenum' total_bugs='0' total_size='25' total_types='1'><ClassStats bugs='0' size='25' interface='false' sourceFile='GlobalReponseCodeEnum.java' class='com.psbc.cpufp.entity.enu.responsecodeenum.GlobalReponseCodeEnum'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.entity.vo' total_bugs='0' total_size='29' total_types='1'><ClassStats bugs='0' size='29' interface='false' sourceFile='XzpDataVo.java' class='com.psbc.cpufp.entity.vo.XzpDataVo'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.exception' total_bugs='0' total_size='4' total_types='1'><ClassStats bugs='0' size='4' interface='false' sourceFile='GlobalException.java' class='com.psbc.cpufp.exception.GlobalException'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.exception.handler' total_bugs='0' total_size='18' total_types='1'><ClassStats bugs='0' size='18' interface='false' sourceFile='GlobalExceptionHandler.java' class='com.psbc.cpufp.exception.handler.GlobalExceptionHandler'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.httpApplication' priority_1='2' total_bugs='4' priority_2='2' total_size='136' total_types='1'><ClassStats bugs='4' size='136' priority_1='2' priority_2='2' interface='false' sourceFile='MyHttpHandler.java' class='com.psbc.cpufp.httpApplication.MyHttpHandler'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.service' total_bugs='0' total_size='5' total_types='2'><ClassStats bugs='0' size='2' interface='true' sourceFile='ClientIWebService.java' class='com.psbc.cpufp.service.ClientIWebService'></ClassStats><ClassStats bugs='0' size='3' interface='true' sourceFile='IWebService.java' class='com.psbc.cpufp.service.IWebService'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.service.impl' total_bugs='1' priority_2='1' total_size='25' total_types='1'><ClassStats bugs='1' size='25' priority_2='1' interface='false' sourceFile='WebServiceImpl.java' class='com.psbc.cpufp.service.impl.WebServiceImpl'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.socket' priority_1='6' total_bugs='7' priority_2='1' total_size='318' total_types='6'><ClassStats bugs='1' size='46' priority_1='1' interface='false' sourceFile='PayServerSocket.java' class='com.psbc.cpufp.socket.PayServerSocket'></ClassStats><ClassStats bugs='4' size='207' priority_1='3' priority_2='1' interface='false' sourceFile='ServerThread.java' class='com.psbc.cpufp.socket.ServerThread'></ClassStats><ClassStats bugs='0' size='9' interface='false' sourceFile='ServerThread.java' class='com.psbc.cpufp.socket.ServerThread$1'></ClassStats><ClassStats bugs='0' size='32' interface='false' sourceFile='SocketListener.java' class='com.psbc.cpufp.socket.SocketListener'></ClassStats><ClassStats bugs='1' size='12' priority_1='1' interface='false' sourceFile='SocketListener.java' class='com.psbc.cpufp.socket.SocketListener$MyHandler'></ClassStats><ClassStats bugs='1' size='12' priority_1='1' interface='false' sourceFile='SocketListener.java' class='com.psbc.cpufp.socket.SocketListener$MyHandler2'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.util' priority_1='2' total_bugs='2' total_size='141' total_types='4'><ClassStats bugs='0' size='7' interface='false' sourceFile='MacUtil.java' class='com.psbc.cpufp.util.MacUtil'></ClassStats><ClassStats bugs='0' size='65' interface='false' sourceFile='SM2KeyPairUtil.java' class='com.psbc.cpufp.util.SM2KeyPairUtil'></ClassStats><ClassStats bugs='2' size='50' priority_1='2' interface='false' sourceFile='Wlhost.java' class='com.psbc.cpufp.util.Wlhost'></ClassStats><ClassStats bugs='0' size='19' interface='false' sourceFile='XmlUtils.java' class='com.psbc.cpufp.util.XmlUtils'></ClassStats></PackageStats><FindBugsProfile><ClassProfile avgMicrosecondsPerInvocation='999' totalMilliseconds='1590' name='edu.umd.cs.findbugs.classfile.engine.ClassDataAnalysisEngine' maxMicrosecondsPerInvocation='5402' standardDeviationMicrosecondsPerInvocation='470' invocations='1592'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='509' totalMilliseconds='804' name='edu.umd.cs.findbugs.classfile.engine.ClassInfoAnalysisEngine' maxMicrosecondsPerInvocation='26813' standardDeviationMicrosecondsPerInvocation='1368' invocations='1578'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='1405' totalMilliseconds='340' name='edu.umd.cs.findbugs.detect.FieldItemSummary' maxMicrosecondsPerInvocation='44696' standardDeviationMicrosecondsPerInvocation='4063' invocations='242'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='1116' totalMilliseconds='270' name='edu.umd.cs.findbugs.detect.FindNoSideEffectMethods' maxMicrosecondsPerInvocation='23459' standardDeviationMicrosecondsPerInvocation='2752' invocations='242'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='1009' totalMilliseconds='244' name='edu.umd.cs.findbugs.classfile.engine.bcel.ValueNumberDataflowFactory' maxMicrosecondsPerInvocation='42606' standardDeviationMicrosecondsPerInvocation='4602' invocations='242'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='1002' totalMilliseconds='240' name='edu.umd.cs.findbugs.classfile.engine.bcel.MethodGenFactory' maxMicrosecondsPerInvocation='207242' standardDeviationMicrosecondsPerInvocation='13341' invocations='240'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='905' totalMilliseconds='218' name='edu.umd.cs.findbugs.classfile.engine.bcel.TypeDataflowFactory' maxMicrosecondsPerInvocation='42113' standardDeviationMicrosecondsPerInvocation='3727' invocations='241'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='318' totalMilliseconds='213' name='edu.umd.cs.findbugs.OpcodeStack$JumpInfoFactory' maxMicrosecondsPerInvocation='23038' standardDeviationMicrosecondsPerInvocation='1126' invocations='672'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='895' totalMilliseconds='212' name='edu.umd.cs.findbugs.classfile.engine.bcel.IsNullValueDataflowFactory' maxMicrosecondsPerInvocation='38996' standardDeviationMicrosecondsPerInvocation='3157' invocations='237'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='744' totalMilliseconds='176' name='edu.umd.cs.findbugs.ba.npe.NullDerefAndRedundantComparisonFinder' maxMicrosecondsPerInvocation='18326' standardDeviationMicrosecondsPerInvocation='1866' invocations='237'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='667' totalMilliseconds='158' name='edu.umd.cs.findbugs.classfile.engine.bcel.CFGFactory' maxMicrosecondsPerInvocation='22550' standardDeviationMicrosecondsPerInvocation='2319' invocations='237'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='644' totalMilliseconds='152' name='edu.umd.cs.findbugs.classfile.engine.bcel.UnconditionalValueDerefDataflowFactory' maxMicrosecondsPerInvocation='23276' standardDeviationMicrosecondsPerInvocation='2147' invocations='237'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='930' totalMilliseconds='120' name='edu.umd.cs.findbugs.detect.FindRefComparison$SpecialTypeAnalysis' maxMicrosecondsPerInvocation='29739' standardDeviationMicrosecondsPerInvocation='3133' invocations='130'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='76' totalMilliseconds='117' name='edu.umd.cs.findbugs.util.TopologicalSort' maxMicrosecondsPerInvocation='2264' standardDeviationMicrosecondsPerInvocation='134' invocations='1529'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='410' totalMilliseconds='99' name='edu.umd.cs.findbugs.detect.NoteDirectlyRelevantTypeQualifiers' maxMicrosecondsPerInvocation='15917' standardDeviationMicrosecondsPerInvocation='1242' invocations='242'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='240' totalMilliseconds='98' name='edu.umd.cs.findbugs.classfile.engine.bcel.JavaClassAnalysisEngine' maxMicrosecondsPerInvocation='25450' standardDeviationMicrosecondsPerInvocation='1339' invocations='408'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='2512' totalMilliseconds='85' name='edu.umd.cs.findbugs.detect.StreamResourceTracker' maxMicrosecondsPerInvocation='4814' standardDeviationMicrosecondsPerInvocation='1324' invocations='34'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='331' totalMilliseconds='80' name='edu.umd.cs.findbugs.detect.EqualsOperandShouldHaveClassCompatibleWithThis' maxMicrosecondsPerInvocation='48489' standardDeviationMicrosecondsPerInvocation='3123' invocations='242'></ClassProfile></FindBugsProfile></FindBugsSummary><ClassFeatures></ClassFeatures><History></History></BugCollection>