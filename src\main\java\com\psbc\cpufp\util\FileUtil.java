//package com.psbc.cpufp.util;
//
//import com.psbc.cpufp.entity.enu.ExploreTypeEum;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//
//import javax.servlet.http.HttpServletResponse;
//import java.io.UnsupportedEncodingException;
//import java.net.URLEncoder;
//import java.nio.charset.StandardCharsets;
//import java.util.Base64;
//
///**
// * FileUtil
// */
//@Service
//public class FileUtil {
//    /**
//     * 文件名不能包含的特殊字符
//     */
//    private static final String REJECT_FILENAME = "/:*?\"<>|";
//
//    /**
//     * @param response     响应
//     * @param downFileName 文件下载名称
//     */
//    public static void setResponse(HttpServletResponse response, String downFileName) {
//        //设置ContentType消息头，这只客户端mime类型
//        response.setContentType("application/octet-stream; charset=UTF-8");
//        //告知浏览器以附件的方式提供下载功能，而不是解析，Content-Disposition指明响应的配置信息，attachment，指明包含附件
//        if (!StringUtils.containsAny(downFileName, REJECT_FILENAME)) {
//            response.setHeader("Content-Disposition", "attachment; filename=" + downFileName);
//        } else {
//            response.setHeader("Content-Disposition", "attachment; filename=" + "default.txt");
//        }
//    }
//
//    /**
//     * 根据浏览器类型对文件名进行编码，解决文件名乱码问题
//     *
//     * @param agent    浏览器类型
//     * @param fileName 文件名
//     * @return 编码后的文件名
//     * @throws UnsupportedEncodingException 不支持的编码格式
//     */
//    public static String agentEncoder(String agent, String fileName) throws UnsupportedEncodingException {
//        switch (ExploreTypeEum.matchType(agent)) {
//            case IE:
//                //IE浏览器
//                fileName = URLEncoder.encode(fileName, "utf-8");
//                fileName = fileName.replace("+", " ");
//                break;
//            case FIREFOX:
//                //火狐浏览器
//                Base64.Encoder encoder = Base64.getEncoder();
//                fileName = "=?utf-8?B?" + encoder.encodeToString(
//                        fileName.getBytes(StandardCharsets.UTF_8)) + "?=";
//                break;
//            case OTHERS:
//                //其他浏览器
//                fileName = URLEncoder.encode(fileName, "utf-8");
//                break;
//            default:
//                break;
//        }
//        return fileName;
//    }
//}
