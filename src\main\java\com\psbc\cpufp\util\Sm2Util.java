//package com.psbc.cpufp.util;
//
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
///**
// * 反欺诈系统部分接口字段加解密工具类
// * */
//@Slf4j
//@Service
//public class Sm2Util {
//
//    private static final Logger log = LoggerFactory.getLogger(Sm2Util.class);
//
//    /**
//     * sm2公钥
//     * */
//    private final String publicKey = "043861c64002f8b33ce04dbe4eb86eb24da0aa82b41421dd8dfdf76861aed262f55af1e81a6490ef1af25dd83cdb337a081b68162f97bc338d840c03dfbe72a17e";
//
//    /**
//     * sm2公钥
//     * */
//    private final String privateKey = "4271333d8a146070ee480bd645209187eacece89c1dde623c7754929595b5019";
//
//    /**
//     * 加密方法 ，传入明文字符串
//     *
//     * */
//    public String sm2String(String str) {
//        String resultStr = str;
//        //        try {
//        //            if (StringUtil.isNotEmpty(str)) {
//        //                resultStr = SM2Utils.encrypt(publicKey, str);
//        //            } else {
//        //                resultStr = SM2Utils.encrypt(publicKey, "-");
//        //            }
//        //        } catch (Exception e) {
//        //            log.error("加密异常：{}", e);
//        //        }
//        return resultStr;
//    }
//
//    /**
//     * 解密方法 ，传入密文字符串
//     *
//     * */
//    public String sm2DecryptString(String str) {
//        String resultStr = str;
//        //        try {
//        //            if (StringUtil.isNotEmpty(str)) {
//        //                resultStr = SM2Utils.decrypt(privateKey, str);
//        //            } else {
//        //                resultStr = SM2Utils.decrypt(privateKey, "-");
//        //            }
//        //        } catch (Exception e) {
//        //            log.error("解密异常：{}", e);
//        //        }
//        return resultStr;
//    }
//}
