package com.psbc.cpufp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "xmysfzjjg")
public class XmysfzjjgProperties {
    private String ip;
    private Integer port;
    private Integer wgPort;
    private Integer httpPort;
    private String message;
}