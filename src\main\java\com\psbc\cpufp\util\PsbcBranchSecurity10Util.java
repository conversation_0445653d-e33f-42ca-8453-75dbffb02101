//package com.psbc.cpufp.util;
//
//import com.alibaba.fastjson.JSONObject;
//import com.psbc.cpufp.sdk.security.EncryptTool;
//import com.psbc.cpufp.sdk.security.GenerateSecretKeyTool;
//import com.psbc.cpufp.sdk.security.SignTool;
//import com.psbc.cpufp.sdk.security.VerifyTool;
//import com.psbc.cpufp.sdk.security.DecryptTool;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.http.HttpHeaders;
//import org.springframework.stereotype.Service;
//
//import java.text.SimpleDateFormat;
////import static com.psbc.cpufp.entity.SimulatorContent.TX_COMM;
//
///**
// *安全认证10工具类
// */
//@Slf4j
//@Service
//public class PsbcBranchSecurity10Util {
//
//
//    private static final Logger log = LoggerFactory.getLogger(PsbcBranchSecurity10Util.class);
//
//
//    /**
//     * 私有化无参构造
//     */
//    private PsbcBranchSecurity10Util() {
//    }
//
//    /**
//     * 分行前置请求报文公共参数
//     */
//    @Data
//    public static class BranchTxComm {
//        public String getBankCertSn() {
//            return bankCertSn;
//        }
//
//        public void setBankCertSn(String bankCertSn) {
//            this.bankCertSn = bankCertSn;
//        }
//
//        public String getEncData() {
//            return encData;
//        }
//
//        public void setEncData(String encData) {
//            this.encData = encData;
//        }
//
//        public String getEncKey() {
//            return encKey;
//        }
//
//        public void setEncKey(String encKey) {
//            this.encKey = encKey;
//        }
//
//        /**
//         * 服务方证书公钥
//         */
//        private String bankCertSn;
//        /**
//         * 业务报文-密文
//         */
//        private String encData;
//        /**
//         * sm4Key随机秘钥-密文
//         */
//        private String encKey;
//    }
//
//    /**
//     * 当前调用安全认证等级-10
//     */
//    private static final String SECURITY_LEVEL_10 = "10";
//
//    /**
//     *  交易时间
//     */
//    private static final String TX_TIME = "txTime";
//
//    /**
//     * 业务跟踪号
//     */
//    private static final String SYS_TRACK_NO = "sysTrackNo";
//
//    /**
//     * 请求方系统号
//     */
//    private static final String REQ_SYS_CODE = "reqSyscode";
//
//    /**
//     * 安全级别
//     */
//    private static final String SECURITY_LEVEL = "securityLevel";
//
//    /**
//     * 用户证书序列号
//     */
//    private static final String USER_CERT_SN = "userCertSN";
//
//    /**
//     * 签名值
//     */
//    private static final String SIGN = "sign";
//
//    /**
//     *报文字段:txComm
//     */
//    private static final String TX_COMM = "txComm";
//
//    /**
//     * 接收json格式请求报文，组装安全认证10加密报文
//     * 部分参数将放在请求headers中
//     *
//     * @param requestBodyJson 业务报文明文
//     * @param requestHeaders  请求header
//     * @return 加密后的报文
//     */
//    public static JSONObject branchSecurity10Request(JSONObject requestBodyJson, PsbcBranchSecurity10Service.PsbcBranchRequestComm headerTxComm, HttpHeaders requestHeaders) throws Exception {
//        //log.info("接收到的业务报文明文; {}", requestBodyJson);
//        //生成17位交易日期
//        String txTime = crateTxTime();
//        //生成业务跟踪号
//        String sysTrackNo = getSysTrackNo(headerTxComm.getReqSysCode(), txTime);
//        //生成sm4key随机秘钥,使用sm4Key随机秘钥加密业务报文
//        String sm4key = GenerateSecretKeyTool.getSm4Key(sysTrackNo);
//        String encData = EncryptTool.sm4Encrypt(sm4key, requestBodyJson.toJSONString(), sysTrackNo);
//        //使用服务方公钥加密sm4Key随机秘钥
//        String encKey =  EncryptTool.sm2Encrypt(headerTxComm.getBranchCertPubKey(), sm4key, sysTrackNo);
//        //签名
//        String dataForSign = sysTrackNo + headerTxComm.getReqSysCode() + txTime + encData + encKey;
//        log.info("待签名数据; {}", dataForSign);
//        //获取用户证书公钥
//        String sign = SignTool.sm2Sign(headerTxComm.getUserCertPublicKey(), headerTxComm.getUserCertPrivateKey(), dataForSign, sysTrackNo);
//        log.info("签名sign值; {}", sign);
//        // 签名sign值
//        requestHeaders.set(SIGN, sign);
//
//        //组装报文公共
//        BranchTxComm txComm = new BranchTxComm();
//        txComm.setBankCertSn(headerTxComm.getBankCertSn());
//        txComm.setEncData(encData);
//        txComm.setEncKey(encKey);
//        JSONObject requestBodyEnc = new JSONObject();
//        requestBodyEnc.put(TX_COMM, txComm);
//        //交易时间
//        requestHeaders.set(TX_TIME, txTime);
//        //业务跟踪号
//        requestHeaders.set(SYS_TRACK_NO, sysTrackNo);
//        //请求方系统号
//        requestHeaders.set(REQ_SYS_CODE, headerTxComm.getReqSysCode());
//        //当前安全认证调用等级
//        requestHeaders.set(SECURITY_LEVEL, SECURITY_LEVEL_10);
//        //用户证书序列号
//        requestHeaders.set(USER_CERT_SN, headerTxComm.getUserCertSn());
//
//        return requestBodyEnc;
//    }
//
//    /**
//     * 安全认证10-响应报文处理
//     *
//     * @param psbcBranchResponseComm 响应公共参数
//     * @return 验签解密后的响应报文明文
//     */
//    public static String branchSecurity10Response(PsbcBranchSecurity10Service.PsbcBranchResponseComm psbcBranchResponseComm) throws Exception {
//        //验签字段，从响应header获取
//        String sign = psbcBranchResponseComm.getSign();
//        //响应码字段，从响应header获取
//        String respCode = psbcBranchResponseComm.getRespCode();
//        //响应描述字段，从响应header获取
//        String respDesc = psbcBranchResponseComm.getRespDesc();
//        //业务跟踪号字段，请求时生成
//        String sysTrackNo = psbcBranchResponseComm.getSysTrackNo();
//        //业务跟踪号字段，从响应报文中获取
//        String encData = psbcBranchResponseComm.getEncData();
//        //组装验签字段
//        String verifyData = respCode + respDesc + encData;
//        // 服务方证书公钥，从配置文件中获取
//        String branchCertPubKey = psbcBranchResponseComm.getBranchCertPubKey();
//        //sm4随机秘钥，请求时生成，响应使用相同值解密响应报文
//        String sm4Key = psbcBranchResponseComm.getSm4Key();
//        //响应报文验签
//        boolean verifyResult = VerifyTool.sm2Verify(branchCertPubKey, verifyData, sign, sysTrackNo);
//        // log.info("验签结果; [{}]", (verifyResult ? "通过" : "不通过"));
//        if (!verifyResult) {
//            // log.error("响应验签数据; {}，验签失败", verifyData);
//            throw new RuntimeException("响应报文，验签失败");
//        }
//        //解密响应报文
//        return DecryptTool.sm4Decrypt(sm4Key, encData, sysTrackNo);
//    }
//
//    /**
//     *生成17位时间日期
//     *
//     * @return 返回生成时间
//     */
//    public static String crateTxTime() {
//        long now = System.currentTimeMillis();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
//        return sdf.format(now);
//    }
//
//    /**生成32位全局业务流水号
//     *
//     * @param reqSysCode 请求系统号
//     * @param txTime 接收请求的时间
//     * @return sysTrackNo 业务流水号
//     */
//    public static String getSysTrackNo(String reqSysCode, String txTime) {
//        //行内接入的请求方系统代码为11位, 行外为12位，这里把UUID拼上，截取拼的字符串的前32位，以后不用UUID，全改成数字
//        String sysTrackNo = null;
//        if (reqSysCode.length() == 11) {
//            sysTrackNo = txTime.substring(0, 14).concat(reqSysCode).concat(String.valueOf((int) ((Math.random() * 9 + 1) * 1000000)));
//        } else {
//            sysTrackNo = txTime.substring(0, 14).concat(reqSysCode).concat(String.valueOf((int) ((Math.random() * 9 + 1) * 100000)));
//        }
//        return sysTrackNo;
//    }
//}