//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.psbc.cpufp.service;

import org.springframework.stereotype.Service;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;

@Service
@WebService(
    targetNamespace = "http://tempuri.org/"
)
public interface ClientIWebService {
    @WebMethod(
        operationName = "Execute",
        action = "http://tempuri.org/Execute"
    )
    @WebResult(
        name = "ExecuteResult",
        targetNamespace = "http://tempuri.org/"
    )
    String Execute(@WebParam(targetNamespace = "http://tempuri.org/",name = "BankID") String bankId, @WebParam(targetNamespace = "http://tempuri.org/",name = "inParmeter") String inParmeter);
}
