#必填 该配置项对于bfes-agent-comm依赖的日志组件是必须的
server:
  port: 6666  # 确保端口号与客户端一致

cxf:
  path: /services
  jaxws:
    client-endpoint-url: http://localhost:6666/services/IWebServiceService

#必填
#响应码的定位信息，通常为系统号前7位，，按照行内响应码标准，响应码通常为:一级分类+ 7位定位信息 + 二级分类 + 6位序列号，位于common-response-enum依赖
enum_config:
  prefix: 3531003

  #监控暴露端点
management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health"

#已启动的kernel的grpc注册地址，如127.0.0.1:20081
soma:
  registryUrl: soma-kernel.cpufp-plat:20081
  #本地内嵌服务启动端口，须保证该端口未被其他进程占用
  remotePort: 9944
  #执行器校验令牌
  execAccessToken: b79558f7390c45d8bec48aa03fabaaa7
  #  execAccessToken: ${soma_token}
  #执行器集群id
  execGroupId: EIDbfc5af50-005c-11ee-883c-fa163e7876be
  #  execGroupId: ${soma_id}
  #该配置项须保证与执行器集群的注册方式一致
  isAutoRegistry: 1
  #日志适配器路径(可选，如果不配置日志打印方式默认为System.out.println)
  loggerAdapter:
  #执行调度任务的最大线程数(可选)
  #  remoteMaxThreadNum: 3
  #当前执行器的版本 要与创建的执行器版本一致
  execVersion: default

logging:
  config: classpath:dev/logback-spring.xml

# 解密秘钥-当spring.datasource.password、spring.redis.password等为密文时，将其解密为明文加载进内存
jasypt:
  encryptor:
    password: 009546321c1f0749c162f3a910112495

spring:
  profiles:
    active: dev
  application:
    name: branch-agent-base-demo
  #必填
  #redis核心配置
  redis:
    #-------------------分行测试环境------------------------#
    host: 127.0.0.1
    password:
#    password: vRbqOxXWoUulbDeL
    # 密文密码
    #password: ENC(45ac...)
#    port: 6380
    port: 6379
  datasource:
    #------------------单数据源版本-测试环境----------------------#
#    password: 19980404Wyp.
    password: 123456
    # 密文密码
    #password: ENC(c9a4...)
    # 设置数据库连接的URL
    url: **************************************************************************
    # 设置数据库连接的用户名
    username: postgres
    # 设置用于验证连接是否有效的SQL语句
    validationQuery: select 'x'
    driver-class-name: org.postgresql.Driver
    #------------------多数据源版本----------------------#
#    one:
#      password: Cpufp@12#
#      # 密文密码
#      #password: ENC(d7c861060a17f0c8f9793e07027935e9)
#      # 设置数据库连接的URL
#      url: **************************************************************************
#      # 设置数据库连接的用户名
#      username: cpufp
#      # 设置用于验证连接是否有效的SQL语句
#      validationQuery: select 'x'
#    two:
#      password: Cpufp@12#
#      # 密文密码
#      #password: ENC(d7c861060a17f0c8f9793e07027935e9)
#      # 设置数据库连接的URL
#      url: **************************************************************************
#      # 设置数据库连接的用户名
#      username: cpufp
#      # 设置用于验证连接是否有效的SQL语句
#      validationQuery: select 'x'
    #------------------多数据源版本----------------------#

mybatis:
  # 配置mapper文件的位置
  mapper-locations: classpath*:mapper/*.xml
  # 配置类型别名包
  type-aliase-package: com.psbc.cpufp

#警银通对接接口配置
jyt:
  #请求方系统号（警银通系统号）
  reqSysCode: ************
  userCert:
    #请求方用户证书序列号(警银通)
    userCertSn: 019614744e23
    #请求方证书公钥(警银通)
    userCertPublicKey: 041e79c83bf6d4493d65e5384e87b8bb7a234f4b9a592b0d9089082ffa0b4701d1a8bd285a9453831793f7a4dc39ed3d2f5fba692d271e657f640ab5bc0a53b419
    #请求方证书私钥(警银通)
    userCertPrivateKey: AJS58r9RHsncGaELFx/czsBtZmLca+stxdHHEJWf+tuZ
  #分行前置
  bankCert:
    #银行证书公钥(分行前置)
    bankCertPublicKey: 04a9ab30feee114284a6155b30eae8426c794b2da2ecf15defb5f0d92c77617017d9c996574ef78666e4455e57aa86f2532ebc74a90d00e6c1cdafc646a8a108ea
    #银行证书序列号(分行前置)
    bankCertSn: 0195f5836b64
  #公安公钥
  gongAn:
    gaCertPublicKey: MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAENWHYBmioTIPpAZFdccHqwgCDG2nTRtea/VkhPUOeBB725+IvI9rsKclWkObCRC1dadxYoF+wL+RXQrpVkWmnMA==
  bankUrl:
    #卡开户信息接口
    601087Url: "http://************:9902/gw/v1/trans/J601087"
    #反欺诈查询客户名下用户接口
    600368Url: "http://************:9902/gw/v1/trans/J600368"
    #止付交易接口
    600010Url: "http://************:9902/gw/v1/trans/J600010"
zhbg:
  url30001: api/admin/xzp/queryYxjfLsxdye
  #\u7efc\u5408\u529e\u516c\u63a5\u53e3\u5730\u5740-\u67e5\u8be2\u76d1\u7ba1\u8d26\u6237\u88ab\u6267\u884c\u53d8\u52a8\u60c5\u51b5\u53cd\u9988\u4fe1\u606f
  url20006: api/admin/xzp/queryJgzhInfo
  #\u4ee5\u4e0b\u53c2\u6570\u6682\u65f6\u6ca1\u7528\uff0c\u5148\u4fdd\u7559
  reqSysNo: *********
  zhbgPublicKey: 0438b7e7107b34d58ace4edea7a3526ba43b10cb430b1e29fe652c8acae44534f967837760983a41ae3d96635623abd70187023a5f21a67c7af6966e06e728c7f3
  selfPublicKey: 04684f10ee3193bdb9194bdb5b6bb630a9b6ce9a22d34fdc36174f887839dfc2d378c804bd251a1a88a2660f310658212a3d72196c4ecfa597458cea46d78710fa
  privateKey: ALLBTAQC5X1EGl2gov0IZRdXy3Sv22jqvFTz2aQhSgqT

xmysfzjjg:
  IP: 127.0.0.1
  PORT: 9702
  WG_PORT: 8888
  HTTP_PORT: 9999
  MESSAGE: <?xml version="1.0" encoding="utf-8"?><content><head><statecode>1</statecode><msg>\u4ea4\u6613\u6210\u529f</msg></head><body><table_account><row><instructionno>*************</instructionno><issuccess>1</issuccess></row></table_account></body></content>
#项目中用到的其他配置
other:
  zhbg_ajdkUrl:  http://127.0.0.1:9091/api/admin/xzp/queryYxjfLsxdye