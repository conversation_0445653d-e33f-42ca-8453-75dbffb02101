//package com.psbc.cpufp.service;
//
//import com.psbc.cpufp.entity.info.TaskCallbackInfo;
//import com.psbc.cpufp.entity.po.FileStreamPo;
//import com.psbc.cpufp.entity.response.GeneralResponse;
//import org.springframework.web.bind.annotation.RequestBody;
//
///**
// * 结果文件调用流程
// *
// * <AUTHOR>
// */
//public interface FileStreamInterface {
//    /**
//     * 结果文件流程：请求参数中的参数
//     * fileStreamPo:{
//     *     fastDfsId：待处理文件在FastDFS上的Id
//     *     subTaskId: 子任务Id，业务运行完成后调用回调时必须
//     *     fileName：待处理文件的文件名称
//     *     trackId：跟踪Id，跟踪文件来源，处理得到的新文件需要和源文件进行关联，将处理结果上传文件时回传回来
//     *     newFileBusinessCode：文件业务代码，生成结果文件需要使用该文件业务流程进行处理，将处理结果上传文件时回传回来
//     *     "extendInfo":{ 扩展信息 包含业务代码 委托方系统代码等
//     *           "key1":"key1",
//     *           "key3":"key2"
//     *       }
//     * }
//     * newFileBusinessCode:
//     *     无：调用旧流程
//     *     有：调用新流程
//     *
//     * @param fileStreamPo 请求参数
//     * @return 是否调用成功
//     */
//    GeneralResponse<TaskCallbackInfo> fileStream(@RequestBody FileStreamPo fileStreamPo);
//}
