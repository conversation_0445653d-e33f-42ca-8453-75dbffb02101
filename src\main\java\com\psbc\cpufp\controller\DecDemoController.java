//package com.psbc.cpufp.controller;
//
//import com.alibaba.fastjson.JSONObject;
//import com.psbc.cpufp.faced.RestTemplateFacade;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.ResponseEntity;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestHeader;
//import org.springframework.web.bind.annotation.RestController;
//
//
///**
// * 请求报文加密接口类
// */
//@RestController
//@Slf4j
//public class DecDemoController {
//
//    private static final Logger log = LoggerFactory.getLogger(DecDemoController.class);
//
//    @Autowired
//    private RestTemplateFacade restTemplateFacade;
//
//    /**
//     * 跳跃被接入透传接口
//     *
//     * @param reqBody 请求报文
//     * @param headers 请求header
//     * @return 响应结果
//     */
//    @PostMapping("/demo/dec/reqBody")
//    public JSONObject decReqBody(@RequestBody JSONObject reqBody, @RequestHeader MultiValueMap<String, String> headers) {
//        // 组装请求报文
//        JSONObject remoteRequestBody = doDec(reqBody);
//        // 组装请求头
//        HttpHeaders httpHeaders = prepareHeader(headers);
//        // 确认响应类型
//        Class<JSONObject> respClass = JSONObject.class;
//        // 确认连接超时时间,单位ms
//        int connectTime = 5000;
//        // 确认响应超时时间,单位ms
//        int readTime = 3000;
//        ResponseEntity<JSONObject> responseEntity = restTemplateFacade.postNorthTrans(httpHeaders, remoteRequestBody, respClass,
//                connectTime, readTime);
//        return responseEntity.getBody();
//    }
//
//    private HttpHeaders prepareHeader(MultiValueMap<String, String> headers) {
//        // 报文体字节长度有变动，content-length如果保持之前的值会导致请求异常，此处移除，请求组装的时候框架会重新计算
//        headers.remove("content-length");
//        HttpHeaders httpHeaders = new HttpHeaders();
//        httpHeaders.addAll(headers);
//        return httpHeaders;
//    }
//
//    /**
//     * 解密密文
//     *
//     * @param reqBody 请求报文
//     * @return 解密后结果
//     */
//    private JSONObject doDec(JSONObject reqBody) {
//        // 具体的处理逻辑
//        String dataAfterDec = "{\"a\":\"a\",\"b\":\"b\"}";
//        reqBody.put("dataAfterDec", dataAfterDec);
//        return reqBody;
//    }
//
//}